#!/usr/bin/env python3
"""
异步并行多智能体系统
实现高性能的并行推理和智能缓存机制
"""

import asyncio
import time
import json
import hashlib
import threading
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import numpy as np
import torch
from collections import OrderedDict
import logging

# 导入智能体将在需要时动态导入

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    processing_time: float
    agent_times: Dict[str, float]
    cache_hits: Dict[str, int]
    cache_misses: Dict[str, int]
    memory_usage: float
    gpu_usage: float
    accuracy: Optional[float] = None

class LRUCache:
    """线程安全的LRU缓存"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.cache = OrderedDict()
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
    
    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                self.cache.move_to_end(key)
                self.hits += 1
                return self.cache[key]
            else:
                self.misses += 1
                return None
    
    def put(self, key: str, value: Any) -> None:
        with self.lock:
            if key in self.cache:
                self.cache.move_to_end(key)
            else:
                if len(self.cache) >= self.max_size:
                    # 删除最久未使用的项
                    self.cache.popitem(last=False)
            self.cache[key] = value
    
    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
            self.hits = 0
            self.misses = 0
    
    def stats(self) -> Dict[str, int]:
        with self.lock:
            total = self.hits + self.misses
            hit_rate = self.hits / total if total > 0 else 0
            return {
                "hits": self.hits,
                "misses": self.misses,
                "hit_rate": hit_rate,
                "size": len(self.cache)
            }

class AsyncAgentPool:
    """异步智能体池"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.semaphore = asyncio.Semaphore(max_workers)
    
    async def submit_task(self, func, *args, **kwargs):
        """提交异步任务"""
        async with self.semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, func, *args, **kwargs)
    
    def shutdown(self):
        """关闭线程池"""
        self.executor.shutdown(wait=True)

class MockLLMAgent:
    """模拟LLM智能体（优化版）"""
    
    def __init__(self, agent_type: str = "single", processing_delay: float = 0.1):
        self.agent_type = agent_type
        self.processing_delay = processing_delay
        self.cache = LRUCache(max_size=500)
    
    def detect(self, text: str, dataset_name: str = None) -> Dict:
        """同步检测方法"""
        # 检查缓存
        cache_key = f"{self.agent_type}_{hash(text)}"
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        # 模拟处理延迟
        time.sleep(self.processing_delay)
        
        # 简单的关键词检测逻辑
        offensive_keywords = ["hate", "stupid", "kill", "die", "worthless", "idiot", "disgusting"]
        text_lower = text.lower()
        offensive_count = sum(1 for keyword in offensive_keywords if keyword in text_lower)
        
        if self.agent_type == "single":
            verdict = 1 if offensive_count >= 1 else 0
            confidence = min(0.8 + offensive_count * 0.1, 0.95)
        else:  # retrieval
            verdict = 1 if offensive_count >= 1 else 0
            confidence = min(0.75 + offensive_count * 0.15, 0.9)
        
        result = {
            "verdict": verdict,
            "confidence": confidence,
            "method": f"llm_{self.agent_type}",
            "keywords_found": offensive_count,
            "processing_time": self.processing_delay
        }
        
        # 缓存结果
        self.cache.put(cache_key, result)
        
        return result
    
    def get_cache_stats(self) -> Dict:
        """获取缓存统计"""
        return self.cache.stats()

class HighPerformanceMultiAgentSystem:
    """高性能多智能体系统"""
    
    def __init__(self, config: Dict = None):
        self.config = config or self._default_config()
        
        # 初始化智能体
        self._initialize_agents()
        
        # 初始化异步组件
        self.agent_pool = AsyncAgentPool(max_workers=3)
        
        # 初始化缓存系统
        self.feature_cache = LRUCache(max_size=1000)
        self.prediction_cache = LRUCache(max_size=2000)
        self.coordination_cache = LRUCache(max_size=1500)
        
        # 性能监控
        self.performance_stats = {
            "total_requests": 0,
            "total_processing_time": 0,
            "cache_stats": {},
            "agent_stats": {},
            "parallel_efficiency": 0
        }
        
        # 批处理队列
        self.batch_queue = asyncio.Queue(maxsize=100)
        self.batch_size = 8
        self.batch_timeout = 0.1  # 100ms
        
    def _default_config(self) -> Dict:
        """默认配置"""
        return {
            "toxigen_config": {
                "model_path": "D:/models/toxigen_roberta",
                "max_length": 512,
                "device": "auto",
                "cache_size": 1000
            },
            "llm_config": {
                "single_delay": 0.08,  # 模拟LLM处理时间
                "retrieval_delay": 0.12
            },
            "parallel_config": {
                "max_workers": 3,
                "enable_batching": True,
                "cache_enabled": True
            }
        }
    
    def _initialize_agents(self):
        """初始化智能体"""
        logger.info("正在初始化智能体...")

        # ToxiGenRoBERTa智能体
        from core_modules.agents import ToxiGenRoBERTaAgent, ToxiGenRoBERTaConfig
        toxigen_config = ToxiGenRoBERTaConfig(**self.config["toxigen_config"])
        self.toxigen_agent = ToxiGenRoBERTaAgent(toxigen_config)

        # LLM智能体（模拟）
        llm_config = self.config["llm_config"]
        self.llm_single_agent = MockLLMAgent("single", llm_config["single_delay"])
        self.llm_retrieval_agent = MockLLMAgent("retrieval", llm_config["retrieval_delay"])

        logger.info("智能体初始化完成")
    
    def _generate_cache_key(self, text: str, method: str = "default") -> str:
        """生成缓存键"""
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()[:16]
        return f"{method}_{text_hash}"
    
    async def _run_toxigen_agent(self, text: str) -> Dict:
        """运行ToxiGenRoBERTa智能体"""
        cache_key = self._generate_cache_key(text, "toxigen")

        # 检查缓存
        cached_result = self.prediction_cache.get(cache_key)
        if cached_result:
            cached_result["from_cache"] = True
            return cached_result

        # 异步执行
        start_time = time.time()
        result = await self.agent_pool.submit_task(
            self.toxigen_agent.detect, text, "classification"
        )
        processing_time = time.time() - start_time

        result["processing_time"] = processing_time
        result["from_cache"] = False

        # 缓存结果
        self.prediction_cache.put(cache_key, result)

        return result

    async def _run_llm_single_agent(self, text: str) -> Dict:
        """运行LLM单智能体"""
        cache_key = self._generate_cache_key(text, "llm_single")
        
        # 检查缓存
        cached_result = self.prediction_cache.get(cache_key)
        if cached_result:
            cached_result["from_cache"] = True
            return cached_result
        
        # 异步执行
        start_time = time.time()
        result = await self.agent_pool.submit_task(
            self.llm_single_agent.detect, text
        )
        processing_time = time.time() - start_time
        
        result["processing_time"] = processing_time
        result["from_cache"] = False
        
        # 缓存结果
        self.prediction_cache.put(cache_key, result)
        
        return result
    
    async def _run_llm_retrieval_agent(self, text: str, dataset_name: str = None) -> Dict:
        """运行LLM检索智能体"""
        cache_key = self._generate_cache_key(text, f"llm_retrieval_{dataset_name or 'default'}")
        
        # 检查缓存
        cached_result = self.prediction_cache.get(cache_key)
        if cached_result:
            cached_result["from_cache"] = True
            return cached_result
        
        # 异步执行
        start_time = time.time()
        result = await self.agent_pool.submit_task(
            self.llm_retrieval_agent.detect, text, dataset_name
        )
        processing_time = time.time() - start_time
        
        result["processing_time"] = processing_time
        result["from_cache"] = False
        
        # 缓存结果
        self.prediction_cache.put(cache_key, result)
        
        return result
    
    async def _parallel_agent_inference(self, text: str, dataset_name: str = None) -> Tuple[List[Dict], Dict]:
        """并行执行智能体推理"""
        start_time = time.time()
        
        # 创建并行任务
        tasks = [
            self._run_toxigen_agent(text),
            self._run_llm_single_agent(text),
            self._run_llm_retrieval_agent(text, dataset_name)
        ]
        
        # 并行执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"智能体 {i} 执行失败: {result}")
                # 提供默认结果
                processed_results.append({
                    "verdict": 0,
                    "confidence": 0.5,
                    "method": f"agent_{i}_fallback",
                    "error": str(result),
                    "processing_time": 0,
                    "from_cache": False
                })
            else:
                processed_results.append(result)
        
        total_time = time.time() - start_time
        
        # 计算并行效率
        sequential_time = sum(r.get("processing_time", 0) for r in processed_results)
        parallel_efficiency = sequential_time / total_time if total_time > 0 else 1
        
        timing_info = {
            "parallel_time": total_time,
            "sequential_time": sequential_time,
            "efficiency": parallel_efficiency,
            "cache_hits": sum(1 for r in processed_results if r.get("from_cache", False))
        }
        
        return processed_results, timing_info
    
    def _coordinate_agents(self, agent_results: List[Dict]) -> Dict:
        """协调智能体结果"""
        # 提取预测和置信度
        verdicts = [result["verdict"] for result in agent_results]
        confidences = [result["confidence"] for result in agent_results]
        methods = [result.get("method", "unknown") for result in agent_results]
        
        # 加权投票
        weights = [1.2, 1.0, 1.1]  # ToxiGenRoBERTa权重稍高
        weighted_score = sum(v * w * c for v, w, c in zip(verdicts, weights, confidences)) / sum(w * c for w, c in zip(weights, confidences))
        
        # 决策逻辑
        if weighted_score > 0.6:
            final_verdict = 1
            final_confidence = min(weighted_score, 0.95)
            decision_method = "weighted_offensive"
        elif weighted_score < 0.4:
            final_verdict = 0
            final_confidence = min(1 - weighted_score, 0.95)
            decision_method = "weighted_normal"
        else:
            # 边界情况，使用最高置信度
            best_idx = np.argmax(confidences)
            final_verdict = verdicts[best_idx]
            final_confidence = confidences[best_idx]
            decision_method = f"confidence_based_{methods[best_idx]}"
        
        return {
            "verdict": final_verdict,
            "confidence": final_confidence,
            "decision_method": decision_method,
            "weighted_score": weighted_score,
            "individual_votes": verdicts,
            "individual_confidences": confidences,
            "agent_methods": methods
        }
    
    async def detect(self, text: str, dataset_name: str = None) -> Dict:
        """主要检测接口"""
        start_time = time.time()
        
        # 检查协调缓存
        coord_cache_key = self._generate_cache_key(text, f"coord_{dataset_name or 'default'}")
        cached_result = self.coordination_cache.get(coord_cache_key)
        
        if cached_result:
            cached_result["from_coordination_cache"] = True
            cached_result["processing_time"] = time.time() - start_time
            return cached_result
        
        try:
            # 并行智能体推理
            agent_results, timing_info = await self._parallel_agent_inference(text, dataset_name)
            
            # 协调决策
            coordinated_result = self._coordinate_agents(agent_results)
            
            # 计算总处理时间
            total_time = time.time() - start_time
            
            # 构建最终结果
            final_result = {
                **coordinated_result,
                "processing_time": total_time,
                "timing_info": timing_info,
                "individual_results": {
                    "toxigen_roberta": agent_results[0],
                    "llm_single": agent_results[1],
                    "llm_retrieval": agent_results[2]
                },
                "from_coordination_cache": False
            }
            
            # 缓存协调结果
            self.coordination_cache.put(coord_cache_key, final_result)
            
            # 更新性能统计
            self.performance_stats["total_requests"] += 1
            self.performance_stats["total_processing_time"] += total_time
            self.performance_stats["parallel_efficiency"] = timing_info["efficiency"]
            
            return final_result
            
        except Exception as e:
            logger.error(f"检测过程中出现错误: {e}")
            return {
                "verdict": 0,
                "confidence": 0.5,
                "error": str(e),
                "processing_time": time.time() - start_time,
                "from_coordination_cache": False
            }
    
    async def batch_detect(self, texts: List[str], dataset_name: str = None) -> List[Dict]:
        """批量检测"""
        if not texts:
            return []
        
        # 创建并行任务
        tasks = [self.detect(text, dataset_name) for text in texts]
        
        # 并行执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"批量检测第 {i} 项失败: {result}")
                processed_results.append({
                    "verdict": 0,
                    "confidence": 0.5,
                    "error": str(result),
                    "processing_time": 0
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        total_requests = self.performance_stats["total_requests"]
        if total_requests == 0:
            return {}
        
        avg_processing_time = self.performance_stats["total_processing_time"] / total_requests
        
        # 获取缓存统计
        cache_stats = {
            "feature_cache": self.feature_cache.stats(),
            "prediction_cache": self.prediction_cache.stats(),
            "coordination_cache": self.coordination_cache.stats()
        }
        
        # 获取智能体缓存统计
        agent_cache_stats = {
            "llm_single": self.llm_single_agent.get_cache_stats(),
            "llm_retrieval": self.llm_retrieval_agent.get_cache_stats()
        }
        
        return {
            "total_requests": total_requests,
            "avg_processing_time": avg_processing_time,
            "parallel_efficiency": self.performance_stats["parallel_efficiency"],
            "cache_stats": cache_stats,
            "agent_cache_stats": agent_cache_stats,
            "memory_info": self._get_memory_info()
        }
    
    def _get_memory_info(self) -> Dict:
        """获取内存信息"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024
            }
        except ImportError:
            return {"error": "psutil not available"}
    
    def clear_caches(self):
        """清理所有缓存"""
        self.feature_cache.clear()
        self.prediction_cache.clear()
        self.coordination_cache.clear()
        logger.info("所有缓存已清理")
    
    def shutdown(self):
        """关闭系统"""
        self.agent_pool.shutdown()
        logger.info("系统已关闭")

# 使用示例
async def main():
    """主函数示例"""
    system = HighPerformanceMultiAgentSystem()
    
    # 测试单个检测
    result = await system.detect("I hate all people from that country")
    print(f"检测结果: {result}")
    
    # 测试批量检测
    texts = [
        "This is a beautiful day",
        "You are so stupid",
        "Thank you for your help"
    ]
    
    batch_results = await system.batch_detect(texts)
    print(f"批量检测结果: {len(batch_results)} 个结果")
    
    # 性能摘要
    performance = system.get_performance_summary()
    print(f"性能摘要: {performance}")
    
    # 关闭系统
    system.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
