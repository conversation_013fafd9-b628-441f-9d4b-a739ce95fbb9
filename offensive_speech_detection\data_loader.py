import os
import pandas as pd
import json

class DataLoader:
    """数据集加载器基类"""
    def __init__(self, dataset_path):
        self.dataset_path = dataset_path
    
    def load_data(self):
        """加载数据集"""
        raise NotImplementedError("子类必须实现此方法")
    
    def get_sample(self, num_samples=10, start_idx=0):
        """获取样本"""
        data = self.load_data()
        if start_idx >= len(data):
            raise ValueError(f"起始索引 {start_idx} 超出数据集大小 {len(data)}")
        
        end_idx = min(start_idx + num_samples, len(data))
        return data[start_idx:end_idx]

class HateSpeechOffensiveDatasetLoader(DataLoader):
    """Hate-Speech-and-Offensive-Language数据集加载器"""
    def __init__(self, dataset_path="datasets/hate-speech-and-offensive-language"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "hate_speech_offensive_test.csv")
        
    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)
        
        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 将标签转换为数字，class=0(仇恨言论)或class=1(攻击性语言)转为1，class=2(无攻击性)转为0
            label = 0 if row["class"] == 2 else 1
            
            data.append({
                "text": row["tweet"],
                "label": label,
                "original_label": int(row["class"]),
                "hate_speech_count": row["hate_speech"],
                "offensive_language_count": row["offensive_language"],
                "neither_count": row["neither"]
            })
        
        return data

class HateSpeechStormfrontDatasetLoader(DataLoader):
    """Hate-Speech-Dataset (Stormfront)数据集加载器"""
    def __init__(self, dataset_path="datasets/hate-speech-dataset"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "hate_speech_stormfront_test.csv")
        
    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)
        
        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 将标签转换为数字，hate -> 1, noHate -> 0
            label = 1 if row["label"] == "hate" else 0
            
            data.append({
                "text": row["text"],
                "label": label,
                "original_label": row["label"],
                "file_id": row["file_id"]
            })
        
        return data

class ImplicitHateDatasetLoader(DataLoader):
    """Implicit-Hate-Corpus数据集加载器"""
    def __init__(self, dataset_path="datasets/implicit-hate-corpus"):
        super().__init__(dataset_path)
        # 使用test.csv作为测试文件
        self.test_file = os.path.join(dataset_path, "implicit_hate_test.csv")

    def load_data(self):
        """加载数据集"""
        df = pd.read_csv(self.test_file)

        # 转换为标准格式
        data = []
        for _, row in df.iterrows():
            # 将标签转换为数字，explicit_hate/implicit_hate -> 1, not_hate -> 0
            label = 0 if row["class"] == "not_hate" else 1

            data.append({
                "text": row["post"],
                "label": label,
                "original_label": row["class"]
            })

        return data

def get_dataset_loader(dataset_name):
    """获取数据集加载器"""
    # 只支持三个英文数据集
    if dataset_name == "hate-speech-and-offensive-language" or dataset_name == "HateSpeechOffensive":
        return HateSpeechOffensiveDatasetLoader()
    elif dataset_name == "hate-speech-dataset" or dataset_name == "HateSpeechStormfront":
        return HateSpeechStormfrontDatasetLoader()
    elif dataset_name == "implicit-hate-corpus" or dataset_name == "ImplicitHate":
        return ImplicitHateDatasetLoader()
    else:
        raise ValueError(f"不支持的数据集: {dataset_name}。仅支持: HateSpeechOffensive, HateSpeechStormfront, ImplicitHate")