#!/usr/bin/env python3
"""
测试三个深度学习模型和敏感词字典的性能脚本
用于了解各个模型的实际效果，为两层多智能体架构设计提供依据

模型包括：
1. ToxiGenRoBERTa - 仇恨言论检测
2. twitter-roberta-base-offensive - 冒犯性语言检测  
3. twitter-roberta-base-sentiment-latest - 情绪分析
4. HurtLex敏感词字典 - 基于词典的检测

作者: AI Assistant
日期: 2025-08-01
"""

import os
import sys
import time
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import json
from dataclasses import dataclass

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class ModelTestResult:
    """模型测试结果"""
    model_name: str
    verdict: int  # 0: 非仇恨/非冒犯, 1: 仇恨/冒犯
    confidence: float
    processing_time: float
    reasoning: str
    additional_info: Dict = None

class HurtLexDictionaryAgent:
    """基于HurtLex敏感词字典的智能体"""
    
    def __init__(self, dict_path="word_dic/hurtlex/hurtlex_EN.tsv"):
        """初始化HurtLex字典智能体"""
        self.dict_path = dict_path
        self.offensive_words = set()
        self.category_words = {}
        self.load_dictionary()
    
    def load_dictionary(self):
        """加载HurtLex字典"""
        try:
            df = pd.read_csv(self.dict_path, sep='\t')
            print(f"✅ 成功加载HurtLex字典，共{len(df)}个词条")

            # 只使用conservative级别的词汇，并过滤掉一些明显的误报类别
            excluded_categories = {'qas', 'or', 'an'}  # 排除可能误报的类别

            # 按类别组织词汇
            for _, row in df.iterrows():
                word = row['lemma'].lower()
                category = row['category']
                level = row['level']

                # 只使用conservative级别且不在排除列表中的词汇
                if level == 'conservative' and category not in excluded_categories:
                    self.offensive_words.add(word)

                if category not in self.category_words:
                    self.category_words[category] = {'conservative': [], 'inclusive': []}

                self.category_words[category][level].append(word)

            print(f"字典统计 (仅使用conservative级别，排除{excluded_categories}):")
            print(f"实际使用的敏感词数量: {len(self.offensive_words)}")
            for category, words in self.category_words.items():
                conservative_count = len(words['conservative'])
                inclusive_count = len(words['inclusive'])
                used = "✓" if category not in excluded_categories else "✗"
                print(f"  {category}: {conservative_count} conservative, {inclusive_count} inclusive {used}")

        except Exception as e:
            print(f"❌ 加载HurtLex字典失败: {e}")
            raise
    
    def detect(self, text: str) -> Dict:
        """检测文本中的敏感词"""
        start_time = time.time()
        
        words = text.lower().split()
        found_words = []
        found_categories = set()
        
        for word in words:
            # 去除标点符号
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word in self.offensive_words:
                found_words.append(clean_word)
                
                # 找到对应的类别
                for category, cat_words in self.category_words.items():
                    if clean_word in cat_words['conservative'] or clean_word in cat_words['inclusive']:
                        found_categories.add(category)
        
        verdict = 1 if found_words else 0
        confidence = min(len(found_words) * 0.3 + 0.1, 1.0) if found_words else 0.0
        
        processing_time = time.time() - start_time
        
        reasoning = f"发现敏感词: {found_words}" if found_words else "未发现敏感词"
        if found_categories:
            reasoning += f", 类别: {list(found_categories)}"
        
        return {
            "verdict": verdict,
            "confidence": confidence,
            "processing_time": processing_time,
            "method": "hurtlex_dictionary",
            "found_words": found_words,
            "found_categories": list(found_categories),
            "reasoning": reasoning
        }

class ModelPerformanceTester:
    """模型性能测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_texts = [
            # 明显的仇恨言论
            "I hate all immigrants, they should go back to their country",
            "Women are stupid and should stay in the kitchen",
            "All Muslims are terrorists and dangerous",
            
            # 隐式仇恨言论
            "Some people just don't belong in this neighborhood",
            "Those people always cause trouble wherever they go",
            "I'm not racist, but they're just different from us",
            
            # 冒犯性但非仇恨
            "This movie is fucking terrible",
            "You're an idiot if you believe that",
            "Damn, that was a stupid mistake",
            
            # 正常文本
            "I love spending time with my family",
            "The weather is beautiful today",
            "I'm excited about the new project at work",
            
            # 边界情况
            "I disagree with their political views",
            "That's not how I would handle the situation",
            "I prefer a different approach to this problem"
        ]
        
        self.models = {}
        self.results = []
    
    def load_models(self):
        """加载所有模型"""
        print("🚀 开始加载模型...")
        
        # 1. 加载ToxiGenRoBERTa
        try:
            from core_modules.agents.toxigen_roberta_agent import ToxiGenRoBERTaAgent, ToxiGenRoBERTaConfig
            config = ToxiGenRoBERTaConfig()
            self.models['toxigen'] = ToxiGenRoBERTaAgent(config)
            print("✅ ToxiGenRoBERTa模型加载成功")
        except Exception as e:
            print(f"❌ ToxiGenRoBERTa模型加载失败: {e}")
            self.models['toxigen'] = None
        
        # 2. 加载twitter-roberta-base-offensive
        try:
            from deploy_and_test_models import ModelDeploymentTester
            tester = ModelDeploymentTester()
            if tester.load_offensive_model():
                self.models['twitter_offensive'] = tester
                print("✅ twitter-roberta-base-offensive模型加载成功")
            else:
                self.models['twitter_offensive'] = None
        except Exception as e:
            print(f"❌ twitter-roberta-base-offensive模型加载失败: {e}")
            self.models['twitter_offensive'] = None
        
        # 3. 加载twitter-roberta-base-sentiment-latest
        try:
            from deploy_and_test_models import ModelDeploymentTester
            if 'twitter_offensive' not in self.models or self.models['twitter_offensive'] is None:
                tester = ModelDeploymentTester()
            else:
                tester = self.models['twitter_offensive']
            
            if tester.load_sentiment_model():
                self.models['twitter_sentiment'] = tester
                print("✅ twitter-roberta-base-sentiment-latest模型加载成功")
            else:
                self.models['twitter_sentiment'] = None
        except Exception as e:
            print(f"❌ twitter-roberta-base-sentiment-latest模型加载失败: {e}")
            self.models['twitter_sentiment'] = None
        
        # 4. 加载HurtLex字典
        try:
            self.models['hurtlex'] = HurtLexDictionaryAgent()
            print("✅ HurtLex敏感词字典加载成功")
        except Exception as e:
            print(f"❌ HurtLex敏感词字典加载失败: {e}")
            self.models['hurtlex'] = None
    
    def test_single_text(self, text: str) -> List[ModelTestResult]:
        """测试单个文本在所有模型上的表现"""
        results = []
        
        print(f"\n📝 测试文本: '{text}'")
        print("-" * 80)
        
        # 测试ToxiGenRoBERTa
        if self.models.get('toxigen'):
            try:
                result = self.models['toxigen'].detect(text)
                test_result = ModelTestResult(
                    model_name="ToxiGenRoBERTa",
                    verdict=result['verdict'],
                    confidence=result.get('all_probabilities', {}).get('LABEL_1', 0.5),
                    processing_time=result['processing_time'],
                    reasoning=result['reasoning'],
                    additional_info=result.get('all_probabilities', {})
                )
                results.append(test_result)
                print(f"🤖 ToxiGenRoBERTa: {test_result.verdict} (置信度: {test_result.confidence:.3f})")
            except Exception as e:
                print(f"❌ ToxiGenRoBERTa测试失败: {e}")
        
        # 测试twitter-roberta-base-offensive
        if self.models.get('twitter_offensive'):
            try:
                result = self.models['twitter_offensive'].test_offensive_model(text)
                if result:
                    # 找到offensive标签的分数
                    offensive_score = 0.0
                    for label, score in result:
                        if 'offensive' in label.lower():
                            offensive_score = score
                            break

                    verdict = 1 if offensive_score > 0.5 else 0
                    test_result = ModelTestResult(
                        model_name="TwitterRoBERTa-Offensive",
                        verdict=verdict,
                        confidence=offensive_score,
                        processing_time=0.0,  # 未单独计时
                        reasoning=f"冒犯性分数: {offensive_score:.3f}",
                        additional_info=dict(result)
                    )
                    results.append(test_result)
                    print(f"🐦 TwitterRoBERTa-Offensive: {test_result.verdict} (置信度: {test_result.confidence:.3f})")
            except Exception as e:
                print(f"❌ TwitterRoBERTa-Offensive测试失败: {e}")

        # 测试twitter-roberta-base-sentiment-latest
        if self.models.get('twitter_sentiment'):
            try:
                result = self.models['twitter_sentiment'].test_sentiment_model(text)
                if result:
                    # 分析情绪结果，负面情绪可能与仇恨言论相关
                    detailed = result['detailed_results']
                    negative_score = 0.0
                    for label, score in detailed:
                        if 'negative' in label.lower():
                            negative_score = score
                            break

                    # 简单映射：负面情绪强度作为潜在仇恨指标
                    verdict = 1 if negative_score > 0.6 else 0
                    test_result = ModelTestResult(
                        model_name="TwitterRoBERTa-Sentiment",
                        verdict=verdict,
                        confidence=negative_score,
                        processing_time=0.0,  # 未单独计时
                        reasoning=f"负面情绪分数: {negative_score:.3f}",
                        additional_info=result
                    )
                    results.append(test_result)
                    print(f"😊 TwitterRoBERTa-Sentiment: {test_result.verdict} (负面情绪: {test_result.confidence:.3f})")
            except Exception as e:
                print(f"❌ TwitterRoBERTa-Sentiment测试失败: {e}")
        
        # 测试HurtLex字典
        if self.models.get('hurtlex'):
            try:
                result = self.models['hurtlex'].detect(text)
                test_result = ModelTestResult(
                    model_name="HurtLex-Dictionary",
                    verdict=result['verdict'],
                    confidence=result['confidence'],
                    processing_time=result['processing_time'],
                    reasoning=result['reasoning'],
                    additional_info={
                        'found_words': result['found_words'],
                        'found_categories': result['found_categories']
                    }
                )
                results.append(test_result)
                print(f"📚 HurtLex字典: {test_result.verdict} (置信度: {test_result.confidence:.3f})")
                if result['found_words']:
                    print(f"   发现敏感词: {result['found_words']}")
            except Exception as e:
                print(f"❌ HurtLex字典测试失败: {e}")
        
        return results
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🔬 开始综合性能测试")
        print("=" * 80)
        
        all_results = []
        
        for i, text in enumerate(self.test_texts, 1):
            print(f"\n[{i}/{len(self.test_texts)}]", end=" ")
            text_results = self.test_single_text(text)
            
            # 记录结果
            for result in text_results:
                all_results.append({
                    'text_id': i,
                    'text': text,
                    'model': result.model_name,
                    'verdict': result.verdict,
                    'confidence': result.confidence,
                    'processing_time': result.processing_time,
                    'reasoning': result.reasoning
                })
        
        self.results = all_results
        self.analyze_results()
    
    def analyze_results(self):
        """分析测试结果"""
        print("\n" + "=" * 80)
        print("📊 测试结果分析")
        print("=" * 80)
        
        if not self.results:
            print("❌ 没有测试结果可分析")
            return
        
        df = pd.DataFrame(self.results)
        
        # 按模型统计
        print("\n🤖 各模型检测统计:")
        model_stats = df.groupby('model').agg({
            'verdict': ['count', 'sum', 'mean'],
            'confidence': 'mean',
            'processing_time': 'mean'
        }).round(3)
        
        for model in df['model'].unique():
            model_data = df[df['model'] == model]
            total = len(model_data)
            positive = model_data['verdict'].sum()
            avg_conf = model_data['confidence'].mean()
            avg_time = model_data['processing_time'].mean()
            
            print(f"  {model}:")
            print(f"    检测为仇恨/冒犯: {positive}/{total} ({positive/total*100:.1f}%)")
            print(f"    平均置信度: {avg_conf:.3f}")
            print(f"    平均处理时间: {avg_time:.3f}s")
        
        # 一致性分析
        print("\n🔄 模型一致性分析:")
        text_consensus = {}
        for text_id in df['text_id'].unique():
            text_data = df[df['text_id'] == text_id]
            verdicts = text_data['verdict'].tolist()
            text_consensus[text_id] = {
                'verdicts': verdicts,
                'consensus': len(set(verdicts)) == 1,
                'positive_ratio': sum(verdicts) / len(verdicts)
            }
        
        consensus_count = sum(1 for v in text_consensus.values() if v['consensus'])
        print(f"  完全一致的文本: {consensus_count}/{len(text_consensus)} ({consensus_count/len(text_consensus)*100:.1f}%)")
        
        # 显示分歧较大的案例
        print("\n⚠️ 模型分歧较大的案例:")
        for text_id, info in text_consensus.items():
            if not info['consensus'] and 0.2 < info['positive_ratio'] < 0.8:
                text = self.test_texts[text_id - 1]
                print(f"  文本 {text_id}: '{text[:50]}...'")
                text_data = df[df['text_id'] == text_id]
                for _, row in text_data.iterrows():
                    print(f"    {row['model']}: {row['verdict']} (置信度: {row['confidence']:.3f})")

def main():
    """主函数"""
    print("🎯 多智能体仇恨言论检测系统 - 模型性能测试")
    print("=" * 80)
    
    tester = ModelPerformanceTester()
    
    # 加载模型
    tester.load_models()
    
    # 运行测试
    tester.run_comprehensive_test()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
