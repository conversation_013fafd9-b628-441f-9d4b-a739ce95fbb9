#!/usr/bin/env python3
"""
三个深度学习模型在完整测试集上的综合评估
对HateSpeechStormfront(478条)、HateSpeechOffensive(1000条)、ImplicitHate(1000条)进行完整测试

作者: AI Assistant
日期: 2025-08-01
"""

import os
import sys
import time
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import json
from dataclasses import dataclass
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/comprehensive_evaluation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class EvaluationResult:
    """评估结果"""
    dataset_name: str
    model_name: str
    predictions: List[int]
    confidences: List[float]
    processing_times: List[float]
    true_labels: List[int]
    accuracy: float
    precision: float
    recall: float
    f1: float
    total_time: float
    error_count: int

class ImprovedHurtLexAgent:
    """改进的HurtLex敏感词智能体"""
    
    def __init__(self, dict_path="word_dic/hurtlex/hurtlex_EN.tsv"):
        self.dict_path = dict_path
        self.high_risk_words = set()
        self.medium_risk_words = set()
        self.category_words = {}
        self.load_dictionary()
    
    def load_dictionary(self):
        """加载并分类HurtLex字典"""
        try:
            df = pd.read_csv(self.dict_path, sep='\t')
            logger.info(f"加载HurtLex字典，共{len(df)}个词条")
            
            # 定义风险等级
            high_risk_categories = {'ps', 'cds', 'dmc', 'asm', 'asf', 're'}  # 种族、诽谤、道德、性器官、犯罪
            medium_risk_categories = {'om', 'pr', 'svp', 'ddp', 'ddf'}  # 性取向、卖淫、罪恶、残疾
            
            for _, row in df.iterrows():
                word = row['lemma'].lower()
                category = row['category']
                level = row['level']
                
                # 只使用conservative级别
                if level == 'conservative':
                    if category in high_risk_categories:
                        self.high_risk_words.add(word)
                    elif category in medium_risk_categories:
                        self.medium_risk_words.add(word)
                
                if category not in self.category_words:
                    self.category_words[category] = {'conservative': [], 'inclusive': []}
                self.category_words[category][level].append(word)
            
            logger.info(f"高风险词汇: {len(self.high_risk_words)}, 中风险词汇: {len(self.medium_risk_words)}")
            
        except Exception as e:
            logger.error(f"加载HurtLex字典失败: {e}")
            raise
    
    def detect(self, text: str) -> Dict:
        """检测文本中的敏感词"""
        start_time = time.time()
        
        words = text.lower().split()
        high_risk_found = []
        medium_risk_found = []
        
        for word in words:
            clean_word = ''.join(c for c in word if c.isalnum())
            if clean_word in self.high_risk_words:
                high_risk_found.append(clean_word)
            elif clean_word in self.medium_risk_words:
                medium_risk_found.append(clean_word)
        
        # 计算风险分数
        total_words = len(words)
        if total_words == 0:
            risk_score = 0.0
        else:
            risk_score = (len(high_risk_found) * 1.0 + len(medium_risk_found) * 0.6) / total_words
        
        verdict = 1 if risk_score > 0.1 else 0
        confidence = min(risk_score * 2, 1.0)  # 归一化到[0,1]
        
        processing_time = time.time() - start_time
        
        return {
            "verdict": verdict,
            "confidence": confidence,
            "processing_time": processing_time,
            "high_risk_words": high_risk_found,
            "medium_risk_words": medium_risk_found,
            "risk_score": risk_score
        }

class ComprehensiveModelEvaluator:
    """综合模型评估器"""
    
    def __init__(self):
        self.models = {}
        self.results = {}
        self.evaluation_results = []
        
        # 创建日志目录
        os.makedirs('logs', exist_ok=True)
        os.makedirs('results', exist_ok=True)
    
    def load_models(self):
        """加载所有模型"""
        logger.info("开始加载模型...")
        
        # 1. 加载ToxiGenRoBERTa
        try:
            from core_modules.agents.toxigen_roberta_agent import ToxiGenRoBERTaAgent, ToxiGenRoBERTaConfig
            config = ToxiGenRoBERTaConfig()
            self.models['toxigen'] = ToxiGenRoBERTaAgent(config)
            logger.info("✅ ToxiGenRoBERTa模型加载成功")
        except Exception as e:
            logger.error(f"❌ ToxiGenRoBERTa模型加载失败: {e}")
            self.models['toxigen'] = None
        
        # 2. 加载twitter-roberta-base-offensive
        try:
            from deploy_and_test_models import ModelDeploymentTester
            tester = ModelDeploymentTester()
            if tester.load_offensive_model():
                self.models['twitter_offensive'] = tester
                logger.info("✅ twitter-roberta-base-offensive模型加载成功")
            else:
                self.models['twitter_offensive'] = None
        except Exception as e:
            logger.error(f"❌ twitter-roberta-base-offensive模型加载失败: {e}")
            self.models['twitter_offensive'] = None
        
        # 3. 加载twitter-roberta-base-sentiment-latest
        try:
            if self.models.get('twitter_offensive'):
                tester = self.models['twitter_offensive']
            else:
                tester = ModelDeploymentTester()
            
            if tester.load_sentiment_model():
                self.models['twitter_sentiment'] = tester
                logger.info("✅ twitter-roberta-base-sentiment-latest模型加载成功")
            else:
                self.models['twitter_sentiment'] = None
        except Exception as e:
            logger.error(f"❌ twitter-roberta-base-sentiment-latest模型加载失败: {e}")
            self.models['twitter_sentiment'] = None
        
        # 4. 加载改进的HurtLex字典
        try:
            self.models['hurtlex'] = ImprovedHurtLexAgent()
            logger.info("✅ 改进的HurtLex敏感词字典加载成功")
        except Exception as e:
            logger.error(f"❌ HurtLex敏感词字典加载失败: {e}")
            self.models['hurtlex'] = None
    
    def load_dataset(self, dataset_name: str):
        """加载数据集"""
        from offensive_speech_detection.data_loader import get_dataset_loader
        
        try:
            loader = get_dataset_loader(dataset_name)
            data = loader.load_data()
            logger.info(f"加载数据集 {dataset_name}，共 {len(data)} 条数据")
            return data
        except Exception as e:
            logger.error(f"加载数据集 {dataset_name} 失败: {e}")
            return None
    
    def evaluate_model_on_dataset(self, model_name: str, dataset_name: str, data: List[Dict]) -> EvaluationResult:
        """在指定数据集上评估单个模型"""
        logger.info(f"开始评估 {model_name} 在 {dataset_name} 上的性能...")
        
        model = self.models.get(model_name)
        if model is None:
            logger.error(f"模型 {model_name} 未加载")
            return None
        
        predictions = []
        confidences = []
        processing_times = []
        true_labels = [item['label'] for item in data]
        error_count = 0
        
        start_time = time.time()
        
        for i, item in enumerate(tqdm(data, desc=f"评估{model_name}")):
            text = item['text']
            try:
                if model_name == 'toxigen':
                    result = model.detect(text)
                    verdict = result['verdict']
                    confidence = result.get('all_probabilities', {}).get('LABEL_1', 0.5)
                    proc_time = result['processing_time']
                
                elif model_name == 'twitter_offensive':
                    result = model.test_offensive_model(text)
                    if result:
                        offensive_score = 0.0
                        for label, score in result:
                            if 'offensive' in label.lower():
                                offensive_score = score
                                break
                        verdict = 1 if offensive_score > 0.5 else 0
                        confidence = offensive_score
                        proc_time = 0.0
                    else:
                        verdict = 0
                        confidence = 0.0
                        proc_time = 0.0
                        error_count += 1
                
                elif model_name == 'twitter_sentiment':
                    result = model.test_sentiment_model(text)
                    if result:
                        detailed = result['detailed_results']
                        negative_score = 0.0
                        for label, score in detailed:
                            if 'negative' in label.lower():
                                negative_score = score
                                break
                        verdict = 1 if negative_score > 0.6 else 0
                        confidence = negative_score
                        proc_time = 0.0
                    else:
                        verdict = 0
                        confidence = 0.0
                        proc_time = 0.0
                        error_count += 1
                
                elif model_name == 'hurtlex':
                    result = model.detect(text)
                    verdict = result['verdict']
                    confidence = result['confidence']
                    proc_time = result['processing_time']
                
                predictions.append(verdict)
                confidences.append(confidence)
                processing_times.append(proc_time)
                
            except Exception as e:
                logger.error(f"处理第{i}条数据时出错: {e}")
                predictions.append(0)
                confidences.append(0.0)
                processing_times.append(0.0)
                error_count += 1
        
        total_time = time.time() - start_time
        
        # 计算评估指标
        accuracy = accuracy_score(true_labels, predictions)
        precision = precision_score(true_labels, predictions, zero_division=0)
        recall = recall_score(true_labels, predictions, zero_division=0)
        f1 = f1_score(true_labels, predictions, zero_division=0)
        
        result = EvaluationResult(
            dataset_name=dataset_name,
            model_name=model_name,
            predictions=predictions,
            confidences=confidences,
            processing_times=processing_times,
            true_labels=true_labels,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1=f1,
            total_time=total_time,
            error_count=error_count
        )
        
        logger.info(f"{model_name} 在 {dataset_name} 上的结果:")
        logger.info(f"  准确率: {accuracy:.3f}")
        logger.info(f"  精确率: {precision:.3f}")
        logger.info(f"  召回率: {recall:.3f}")
        logger.info(f"  F1分数: {f1:.3f}")
        logger.info(f"  总耗时: {total_time:.2f}s")
        logger.info(f"  错误数: {error_count}")
        
        return result

    def run_comprehensive_evaluation(self):
        """运行综合评估"""
        logger.info("开始综合评估...")

        datasets = ['HateSpeechStormfront', 'HateSpeechOffensive', 'ImplicitHate']
        models = ['toxigen', 'twitter_offensive', 'twitter_sentiment', 'hurtlex']

        for dataset_name in datasets:
            logger.info(f"\n{'='*60}")
            logger.info(f"评估数据集: {dataset_name}")
            logger.info(f"{'='*60}")

            # 加载数据集
            data = self.load_dataset(dataset_name)
            if data is None:
                continue

            # 在每个模型上评估
            for model_name in models:
                if self.models.get(model_name) is not None:
                    result = self.evaluate_model_on_dataset(model_name, dataset_name, data)
                    if result:
                        self.evaluation_results.append(result)

        # 保存结果
        self.save_results()
        self.analyze_results()
        self.detailed_analysis()

    def save_results(self):
        """保存评估结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存详细结果
        results_data = []
        for result in self.evaluation_results:
            results_data.append({
                'dataset': result.dataset_name,
                'model': result.model_name,
                'accuracy': result.accuracy,
                'precision': result.precision,
                'recall': result.recall,
                'f1': result.f1,
                'total_time': result.total_time,
                'error_count': result.error_count,
                'avg_processing_time': np.mean(result.processing_times) if result.processing_times else 0
            })

        results_df = pd.DataFrame(results_data)
        results_file = f'results/comprehensive_evaluation_{timestamp}.csv'
        results_df.to_csv(results_file, index=False)
        logger.info(f"评估结果已保存到: {results_file}")

        # 保存详细预测结果
        for result in self.evaluation_results:
            detail_file = f'results/predictions_{result.model_name}_{result.dataset_name}_{timestamp}.json'
            detail_data = {
                'dataset': result.dataset_name,
                'model': result.model_name,
                'predictions': result.predictions,
                'confidences': result.confidences,
                'true_labels': result.true_labels,
                'metrics': {
                    'accuracy': result.accuracy,
                    'precision': result.precision,
                    'recall': result.recall,
                    'f1': result.f1
                }
            }
            with open(detail_file, 'w') as f:
                json.dump(detail_data, f, indent=2)

    def analyze_results(self):
        """分析评估结果"""
        logger.info("\n" + "="*80)
        logger.info("综合分析结果")
        logger.info("="*80)

        if not self.evaluation_results:
            logger.error("没有评估结果可分析")
            return

        # 按数据集分析
        datasets = list(set(r.dataset_name for r in self.evaluation_results))
        models = list(set(r.model_name for r in self.evaluation_results))

        for dataset in datasets:
            logger.info(f"\n📊 数据集: {dataset}")
            logger.info("-" * 60)

            dataset_results = [r for r in self.evaluation_results if r.dataset_name == dataset]

            for result in dataset_results:
                logger.info(f"  {result.model_name:20} | "
                          f"Acc: {result.accuracy:.3f} | "
                          f"P: {result.precision:.3f} | "
                          f"R: {result.recall:.3f} | "
                          f"F1: {result.f1:.3f}")

        # 模型整体表现
        logger.info(f"\n🤖 模型整体表现 (平均值)")
        logger.info("-" * 60)

        for model in models:
            model_results = [r for r in self.evaluation_results if r.model_name == model]
            if model_results:
                avg_acc = np.mean([r.accuracy for r in model_results])
                avg_prec = np.mean([r.precision for r in model_results])
                avg_rec = np.mean([r.recall for r in model_results])
                avg_f1 = np.mean([r.f1 for r in model_results])

                logger.info(f"  {model:20} | "
                          f"Acc: {avg_acc:.3f} | "
                          f"P: {avg_prec:.3f} | "
                          f"R: {avg_rec:.3f} | "
                          f"F1: {avg_f1:.3f}")

    def detailed_analysis(self):
        """详细分析"""
        logger.info("\n" + "="*80)
        logger.info("详细分析")
        logger.info("="*80)

        # 分析模型一致性
        self.analyze_model_agreement()

        # 分析错误案例
        self.analyze_error_cases()

        # 分析置信度分布
        self.analyze_confidence_distribution()

    def analyze_model_agreement(self):
        """分析模型一致性"""
        logger.info("\n🔄 模型一致性分析")
        logger.info("-" * 60)

        datasets = list(set(r.dataset_name for r in self.evaluation_results))

        for dataset in datasets:
            dataset_results = [r for r in self.evaluation_results if r.dataset_name == dataset]
            if len(dataset_results) < 2:
                continue

            logger.info(f"\n数据集: {dataset}")

            # 计算两两一致性
            for i in range(len(dataset_results)):
                for j in range(i+1, len(dataset_results)):
                    result1 = dataset_results[i]
                    result2 = dataset_results[j]

                    agreement = np.mean(np.array(result1.predictions) == np.array(result2.predictions))
                    logger.info(f"  {result1.model_name} vs {result2.model_name}: {agreement:.3f}")

    def analyze_error_cases(self):
        """分析错误案例"""
        logger.info("\n❌ 错误案例分析")
        logger.info("-" * 60)

        for result in self.evaluation_results:
            predictions = np.array(result.predictions)
            true_labels = np.array(result.true_labels)

            # 假阳性和假阴性
            false_positives = np.sum((predictions == 1) & (true_labels == 0))
            false_negatives = np.sum((predictions == 0) & (true_labels == 1))

            logger.info(f"\n{result.model_name} - {result.dataset_name}:")
            logger.info(f"  假阳性: {false_positives}")
            logger.info(f"  假阴性: {false_negatives}")

    def analyze_confidence_distribution(self):
        """分析置信度分布"""
        logger.info("\n📈 置信度分布分析")
        logger.info("-" * 60)

        for result in self.evaluation_results:
            confidences = np.array(result.confidences)

            logger.info(f"\n{result.model_name} - {result.dataset_name}:")
            logger.info(f"  平均置信度: {np.mean(confidences):.3f}")
            logger.info(f"  置信度标准差: {np.std(confidences):.3f}")
            logger.info(f"  最小置信度: {np.min(confidences):.3f}")
            logger.info(f"  最大置信度: {np.max(confidences):.3f}")

def main():
    """主函数"""
    logger.info("🎯 开始三个深度学习模型的综合评估")
    logger.info("="*80)

    evaluator = ComprehensiveModelEvaluator()

    # 加载模型
    evaluator.load_models()

    # 运行综合评估
    evaluator.run_comprehensive_evaluation()

    logger.info("\n✅ 综合评估完成！")

if __name__ == "__main__":
    main()
