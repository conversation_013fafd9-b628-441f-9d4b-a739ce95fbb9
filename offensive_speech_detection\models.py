import os
import time
import json
import re
import requests
from openai import OpenAI
from types import SimpleNamespace
from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline, AutoModel
import torch
import logging
from transformers import logging as transformers_logging
import chromadb
from chromadb.config import Settings
import numpy as np
import pandas as pd
from tqdm import tqdm
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass


# 添加全局模型缓存字典和锁
import threading
_MODEL_CACHE = {}
_MODEL_CACHE_LOCK = threading.Lock()

# 清除模型缓存的函数
def clear_model_cache(model_path=None):
    """清除模型缓存
    
    Args:
        model_path: 如果提供，只清除指定模型的缓存；否则清除所有缓存
    """
    global _MODEL_CACHE
    if model_path:
        cache_key = f"local_{model_path}"
        if cache_key in _MODEL_CACHE:
            del _MODEL_CACHE[cache_key]
    else:
        _MODEL_CACHE.clear()

def create_prompt(role):
    """Creates a system prompt based on the agent's role."""
    return BASE_PROMPTS.get(role, "")

# Initialize client
client = OpenAI(
    base_url="https://yunwu.ai/v1",
    api_key="sk-I1h67Bmh1AlgvSOZ0yVfiA3zuri0IkdH3na7FQhRqqRUA7uz",
    timeout=120
)

# 支持的API模型列表
SUPPORTED_API_MODELS = {
    # OpenAI模型
    "gpt-3.5-turbo-0125": {"provider": "api"},
    "gpt-4o-2024-08-06": {"provider": "api"},
    "gpt-4-0613": {"provider": "api"},
    # Claude模型
    "claude-3-5-sonnet-20240620": {"provider": "api"},
    "claude-3-7-sonnet-20250219": {"provider": "api"},
    # Gemini模型
    "gemini-1.5-flash": {"provider": "api"},
    "gemini-1.5-pro": {"provider": "api"},
    # DeepSeek模型
    "deepseek-v3": {"provider": "api"},
    "deepseek-chat": {"provider": "api"},
}

# Local model client
class LocalModelClient:
    """Local deployed model client using HuggingFace Transformers"""
    def __init__(self, model_path, timeout=120, force_reload=False):
        self.model_path = model_path
        self.timeout = timeout
        self.is_chatglm = "chatglm" in self.model_path.lower() # 识别是否为ChatGLM模型
        # 仅当路径中包含'qwen'且不包含'qwen2'时，才识别为需要特殊处理的Qwen模型
        self.is_qwen = "qwen" in self.model_path.lower() and "qwen2" not in self.model_path.lower()
        
        # 禁用pipeline警告
        transformers_logging.set_verbosity_error()
        # 禁用PyTorch额外警告
        logging.getLogger("torch.distributed.distributed_c10d").setLevel(logging.ERROR)
        
        # 检查是否已经有缓存的模型实例（线程安全）
        global _MODEL_CACHE, _MODEL_CACHE_LOCK
        cache_key = f"local_{model_path}"

        with _MODEL_CACHE_LOCK:
            # 如果强制重新加载，则清除现有缓存
            if force_reload and cache_key in _MODEL_CACHE:
                print(f"🔄 强制重新加载模型: {model_path}")
                del _MODEL_CACHE[cache_key]

            if cache_key in _MODEL_CACHE:
                print(f"♻️ 从缓存加载模型: {model_path}")
                cached_instance = _MODEL_CACHE[cache_key]
                self.model = cached_instance["model"]
                self.tokenizer = cached_instance["tokenizer"]
                self.pipe = cached_instance.get("pipe") # 使用.get以安全处理ChatGLM没有pipe的情况
            else:
                print(f"🚀 首次加载模型: {model_path}")
                self._initialize_model()
                # 缓存模型实例
                _MODEL_CACHE[cache_key] = {
                    "model": self.model,
                    "tokenizer": self.tokenizer,
                    "pipe": getattr(self, 'pipe', None) # 只有在存在时才缓存pipe
                }
                print(f"✅ 模型已缓存: {model_path}")
    
    def _initialize_model(self):
        """Initialize the model and tokenizer"""
        # 加载tokenizer并应用补丁（如果需要）
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path, trust_remote_code=True)
        
        if self.is_chatglm:
            # --- ChatGLM/ChatGLM2 特殊处理 ---
            # 1. 应用猴子补丁
            if not hasattr(self.tokenizer.__class__, 'old_pad'):
                TokenizerClass = self.tokenizer.__class__
                setattr(TokenizerClass, "old_pad", TokenizerClass._pad)
                def new_pad(self, *args, **kwargs):
                    kwargs.pop("padding_side", None)
                    return self.old_pad(*args, **kwargs)
                setattr(TokenizerClass, "_pad", new_pad)

            # 2. 使用 AutoModel 加载
            self.model = AutoModel.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                device_map="auto",
                torch_dtype=torch.float16
            ).eval()
            
            # 3. ChatGLM不需要pipeline
            self.pipe = None
        else:
            # --- 其他模型的标准处理流程 (Qwen, Qwen2, etc.) ---
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                self.tokenizer.pad_token_id = self.tokenizer.eos_token_id

            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path, 
                device_map="auto",
                torch_dtype=torch.float16,
                trust_remote_code=True # 为Qwen1等也开启
            )
        
            self.pipe = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer
            )
    
    def chat_completions_create(self, model, messages, temperature=0.1):
        """Simulate OpenAI interface format to call local model"""
        try:
            # Build prompt
            system_content = ""
            prompt = ""
            
            for message in messages:
                if message["role"] == "system":
                    system_content = message["content"]
                elif message["role"] == "user":
                    prompt += message["content"] + "\n"

            # 根据模型类型选择不同的推理路径
            if self.is_chatglm:
                # --- ChatGLM/ChatGLM2 推理路径 ---
                # 修改系统提示词，强制要求 JSON 格式输出
                enhanced_system_content = system_content + """

CRITICAL INSTRUCTIONS FOR OUTPUT FORMAT:
1. YOUR RESPONSE MUST BE A VALID JSON OBJECT AND NOTHING ELSE
2. DO NOT INCLUDE ANY TEXT OUTSIDE THE JSON STRUCTURE
3. DO NOT ADD MARKDOWN CODE BLOCKS (```)
4. DO NOT ADD ANY EXPLANATIONS BEFORE OR AFTER THE JSON
5. ENSURE THE JSON IS PROPERLY FORMATTED WITH QUOTES AROUND KEYS AND STRING VALUES
6. VERDICT MUST BE A NUMBER (0 OR 1, NOT A STRING)
7. EXAMPLE OF THE EXPECTED FORMAT: {"verdict": 1, "explanation": "This text is offensive because..."}

"""
                
                full_prompt = f"{enhanced_system_content}\n\n{prompt.strip()}"
                response, _ = self.model.chat(self.tokenizer, full_prompt, history=[])
                
                # 后处理 ChatGLM 输出，确保返回有效的 JSON
                raw_response = response.strip()
                
                # 尝试提取 JSON 内容
                try:
                    # 尝试直接解析（如果模型已经输出了有效 JSON）
                    import json
                    json_obj = json.loads(raw_response)
                    generated_text = raw_response
                except:
                    # 如果直接解析失败，尝试从文本中提取并构建 JSON
                    import re
                    
                    # 1. 首先尝试从原始文本中提取任何JSON格式内容
                    json_match = re.search(r'({[\s\S]*})', raw_response)
                    if json_match:
                        try:
                            json_str = json_match.group(1)
                            json_obj = json.loads(json_str)
                            generated_text = json_str
                            print("成功提取JSON格式内容")
                        except:
                            # JSON提取失败，继续尝试其他方法
                            pass
                    
                    # 2. 尝试从"Verdict: X"和"Explanation: Y"格式中提取内容
                    verdict_match = re.search(r'Verdict:?\s*(\d+)', raw_response, re.IGNORECASE)
                    explanation_match = re.search(r'Explanation:?\s*(.*?)(?:\n\n|$)', raw_response, re.DOTALL|re.IGNORECASE)
                    
                    # 3. 如果没找到明确的verdict标记，尝试寻找是否/否的判断
                    if not verdict_match:
                        # 寻找表示offensive/非offensive的关键词
                        if re.search(r'(offensive|侮辱|冒犯|攻击性|不尊重|不礼貌|辱骂|粗俗|粗鲁|恶意|歧视|仇恨|污蔑)', raw_response, re.IGNORECASE):
                            verdict = 1  # 判定为offensive
                        elif re.search(r'(not offensive|non-offensive|非冒犯|没有冒犯|不含冒犯|不具攻击性)', raw_response, re.IGNORECASE):
                            verdict = 0  # 判定为non-offensive
                        else:
                            # 查找数字1或0
                            digit_match = re.search(r'\b([01])\b', raw_response)
                            verdict = int(digit_match.group(1)) if digit_match else 0
                    else:
                        verdict = int(verdict_match.group(1))
                    
                    # 提取解释文本
                    if explanation_match:
                        explanation = explanation_match.group(1).strip()
                    else:
                        # 如果没有明确的explanation标记，取verdict判断附近的文本作为解释
                        # 先去掉常见的无关文本
                        cleaned_text = re.sub(r'(```json|```|verdict:|explanation:)', '', raw_response, flags=re.IGNORECASE)
                        # 取最多100个字符的有意义解释
                        explanation = cleaned_text.strip()[:150] + "..."
                        if not explanation or explanation.isspace():
                            explanation = "提取自模型的自然语言回答"
                    
                    # 构建有效的 JSON 字符串
                    json_obj = {
                        "verdict": verdict,
                        "explanation": explanation
                    }
                    generated_text = json.dumps(json_obj)
            elif self.is_qwen:
                # --- Qwen-7B (非Chat版) 特殊处理 ---
                # Qwen-7B基础版不是对话模型，更适合few-shot completion风格
                
                # 将所有信息合并成一个prompt
                full_prompt = f"{system_content}\n\n{prompt.strip()}"
                
                # 为模型提供一个清晰的结尾，引导它生成我们想要的内容
                full_prompt += "\n\nJSON Response:"

                response = self.pipe(
                    full_prompt,
                    max_new_tokens=512,
                    temperature=temperature,
                    do_sample=True,
                    top_p=0.2,
                    top_k=10,
                    return_full_text=False,
                    repetition_penalty=1.1,
                    pad_token_id=self.tokenizer.pad_token_id
                )
                generated_text = response[0]["generated_text"].strip()
            else:
                # --- 其他模型的标准推理路径 ---
                # 检查tokenizer是否支持聊天模板
                if hasattr(self.tokenizer, 'chat_template') and self.tokenizer.chat_template:
                    messages_formatted = []
                    if system_content:
                        messages_formatted.append({"role": "system", "content": system_content})
                    
                    user_content = prompt.strip()
                    if user_content:
                        messages_formatted.append({"role": "user", "content": user_content})

                    full_prompt = self.tokenizer.apply_chat_template(
                        messages_formatted,
                        tokenize=False,
                        add_generation_prompt=True
                    )
                else:
                    user_content = prompt.strip()
                    if system_content:
                        full_prompt = f"USER: {system_content}\n\n{user_content}\nASSISTANT:"
                    else:
                        full_prompt = f"USER: {user_content}\nASSISTANT:"
                
                # 调用模型
                response = self.pipe(
                    full_prompt,
                    max_new_tokens=512,
                    temperature=temperature,
                    do_sample=True,
                    top_p=0.2,
                    top_k=10,
                    return_full_text=False,
                    repetition_penalty=1.1,
                    pad_token_id=self.tokenizer.pad_token_id
                )
                generated_text = response[0]["generated_text"].strip()

            # 构造类似OpenAI的响应格式
            return SimpleNamespace(
                choices=[SimpleNamespace(
                    message=SimpleNamespace(content=generated_text)
                )]
            )
        except Exception as e:
            print(f"Local model error: {str(e)}")
            raise Exception(f"Local model call failed: {str(e)}")

# Ollama model client
class OllamaModelClient:
    """Ollama API client"""
    def __init__(self, base_url="http://localhost:11434", timeout=120):
        self.base_url = base_url
        self.timeout = timeout
    
    def chat_completions_create(self, model, messages, temperature=0.1):
        """Simulate OpenAI interface format to call Ollama API"""
        # Build prompt
        system_content = ""
        prompt = ""
        
        for message in messages:
            if message["role"] == "system":
                system_content = message["content"]
            elif message["role"] == "user":
                prompt += message["content"] + "\n"
        
        if system_content:
            prompt = f"{system_content}\n\n{prompt}"
        
        # Build API request
        url = f"{self.base_url}/api/generate"
        data = {
            "model": model,
            "prompt": prompt,
            "temperature": temperature,
            "stream": False
        }
        
        try:
            response = requests.post(url, json=data, timeout=self.timeout)
            response.raise_for_status()
            result = response.json()
            
            # Construct response similar to OpenAI format
            return SimpleNamespace(
                choices=[SimpleNamespace(
                    message=SimpleNamespace(content=result.get("response", ""))
                )]
            )
        except Exception as e:
            raise Exception(f"Ollama API call failed: {str(e)}")

# Create a simple namespace class to simulate OpenAI return structure
class SimpleNamespace:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

# Prompts are now unified into a single structure, combining role and format instructions.
# The format is based on the 'mistral' style instructions.
BASE_PROMPTS = {
    "single_agent": """You are an offensive speech detection system. Your task is to determine whether the given text contains offensive speech.

Evaluation criteria:
- Offensive speech (1): Contains explicit insults, threats, hatred, or derogatory content directed at specific groups or individuals
- Non-offensive speech (0): Does not contain offensive content, or merely expresses opinions or factual statements

Please analyze the text and provide your judgment.

Your output MUST be a valid JSON object in the following format:
{
  "verdict": 0 or 1,
  "explanation": "Brief explanation of why the speech is/is not offensive"
}

IMPORTANT:
1. Your response MUST ONLY contain the JSON object, nothing else
2. The "verdict" field MUST be either 0 or 1 (numeric), not a string
3. Ensure your JSON is properly formatted and valid
4. Keep your explanation concise, maximum 50 words
5. Do NOT use line breaks or special characters in your explanation""",

    "retrieval_agent": """You are an offensive speech detection system with retrieval capability. 
Your task is to determine whether the given text contains offensive speech based on the provided reference examples.

Follow these steps to make your decision:
1.  **Analyze the Input Text**: Carefully read the user's text to understand its content and context.
2.  **Review Reference Examples**: Examine each of the retrieved examples below. Pay attention to the text, its verdict (0 for non-offensive, 1 for offensive), and the implied reasoning.
3.  **Compare and Contrast**: Compare the user's text to the reference examples. Does it share characteristics with the offensive or non-offensive examples?
4.  **Formulate a Verdict**: Based on your comparison, decide if the user's text is offensive.

**Reference Examples:**
{{retrieved_examples}}

Based on your analysis, provide a verdict for the following text. Your output MUST be a valid JSON object in the following format:
{
  "verdict": 0 or 1,
  "explanation": "Brief explanation of why the speech is/is not offensive, based on your analysis.",
  "reference_usage": "Explain how the reference examples influenced your decision. Mention which examples were most helpful and why."
}

**IMPORTANT RULES:**
- Your response MUST ONLY contain the JSON object.
- The "verdict" field MUST be a number (0 or 1).
- All explanations must be concise and in English.
- Do not include newline characters (`\n`) inside the JSON fields.
""",

    "evidence_scout": """[Agent removed during restructuring]""",
    "intent_classifier": """[Agent removed during restructuring]""",
    "multi_analyst": """[Agent removed during restructuring]""",
    "chief_adjudicator": """[Agent removed during restructuring]"""
}

# Function to create prompt for a specific role
def create_prompt(role):
    """Create a prompt for a specific role."""
    return BASE_PROMPTS.get(role, "")

# System prompts will be generated dynamically
class OffensiveSpeechDetector:
    """Offensive speech detection base class"""
    def __init__(self, model="gpt-3.5-turbo-0125", provider="api", ollama_base_url="http://localhost:11434", local_model_path=None):
        self.model = model
        self.provider = provider.lower()  # Model provider: api or ollama
        self.ollama_base_url = ollama_base_url
        self.local_model_path = local_model_path
    
    def create_prompt(self, role):
        """Creates a system prompt based on the agent's role."""
        return BASE_PROMPTS.get(role, "")

    def detect(self, text):
        """Placeholder for detection method"""
        raise NotImplementedError("Each detector must implement its own detect method.")
    
    def _call_model(self, prompt, message):
        """Call different models based on provider"""
        try:
            # Add explicit instruction for JSON formatting
            enhanced_prompt = prompt + "\n\nCRITICAL: Your response MUST be a valid JSON object and nothing else. Do not include any text before or after the JSON. Keep explanations concise and ensure proper closing of all JSON brackets."
            
            if self.provider == "api":
                response = client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": enhanced_prompt},
                        {"role": "user", "content": message}
                    ],
                    temperature=0.1,  # Lower temperature for more deterministic outputs
                    top_p=0.2,
                    max_tokens=512
                )
                content = response.choices[0].message.content
                
                # Handling possible empty responses from some models
                if (not content or content.strip() == ""):
                    # 返回一个默认的有效JSON
                    if "single_agent" in enhanced_prompt or "retrieval_agent" in enhanced_prompt:
                        return '{"verdict": 0, "explanation": "Unable to determine verdict due to processing error."}'
                    else:
                        return '{"error": "Empty response from model"}'
                
                return content
            elif self.provider == "ollama":
                ollama_client = OllamaModelClient(base_url=self.ollama_base_url)
                response = ollama_client.chat_completions_create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": enhanced_prompt},
                        {"role": "user", "content": message}
                    ],
                    temperature=0.1  # Lower temperature for more deterministic outputs
                )
                content = response.choices[0].message.content
                
                # 处理可能的空响应
                if not content or content.strip() == "":
                    # 返回一个默认的有效JSON
                    if "single_agent" in enhanced_prompt or "retrieval_agent" in enhanced_prompt:
                        return '{"verdict": 0, "explanation": "Unable to determine verdict due to processing error."}'
                    else:
                        return '{"error": "Empty response from model"}'
                
                return content
            elif self.provider == "local":
                # 优先使用共享的本地客户端，避免重复加载模型
                if hasattr(self, '_shared_local_client') and self._shared_local_client:
                    local_client = self._shared_local_client
                else:
                    local_client = LocalModelClient(model_path=self.local_model_path)

                response = local_client.chat_completions_create(
                    model=self.model,
                        messages=[
                            {"role": "system", "content": enhanced_prompt},
                            {"role": "user", "content": message}
                        ],
                        temperature=0.1
                    )
                content = response.choices[0].message.content
                
                # 处理可能的空响应
                if not content or content.strip() == "":
                    # 返回一个默认的有效JSON
                    if "single_agent" in enhanced_prompt or "retrieval_agent" in enhanced_prompt:
                        return '{"verdict": 0, "explanation": "Unable to determine verdict due to processing error."}'
                    else:
                        return '{"error": "Empty response from model"}'
                
                return content
            else:
                raise ValueError(f"Unknown provider: {self.provider}")
        except Exception as e:
            print(f"Error calling model: {e}")
            return None

    def _call_model_with_retry(self, prompt, message, attempts=4, delay=5):
        """Call model with retry mechanism, focusing on getting a non-empty response."""
        for i in range(attempts):
            try:
                response_text = self._call_model(prompt, message)
                if response_text and response_text.strip():
                    return response_text  # Return as soon as we get a non-empty response
                else:
                    print(f"Warning: Attempt {i+1}/{attempts} returned an empty response.")
            except Exception as e:
                print(f"Warning: Attempt {i+1}/{attempts} failed with error: {e}")
            
            if i < attempts - 1:
                print(f"Retrying in {delay} seconds...")
                time.sleep(delay)
        
        print(f"Error: All {attempts} attempts failed to get a valid response from the model.")
        return None # Return None if all attempts fail

    def _fix_json_errors(self, json_string):
        """Attempt to fix common JSON errors, like missing closing brackets."""
        # Add a closing brace if it seems to be missing
        json_string = json_string.strip()
        if json_string.count('{') > json_string.count('}'):
            json_string += '}'
        
        # Add closing bracket and brace if a list of objects seems to be unclosed
        if json_string.endswith('},'):
            json_string = json_string[:-1] + ']}'

        # Remove trailing commas that can cause errors
        json_string = re.sub(r',\s*([}\]])', r'\1', json_string)
        
        return json_string

    def _parse_json_safely(self, response_text, agent_type, original_text=None):
        """Parse JSON safely, handling various error cases and attempting to fix them."""
        if not response_text:
            print(f"Error: Received no response text for {agent_type}. Returning default structure.")
            return self._get_default_structure(agent_type, original_text)
        
        # Fix for invalid escape sequence from some models like Vicuna
        response_text = response_text.replace('\\_', '_')
        
        # 1. 首先尝试直接JSON解析
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            print(f"Initial JSON parsing failed for {agent_type}, attempting alternative methods.")
            
        # 2. 尝试从Markdown代码块和额外文本中提取JSON
        # Unified parsing logic
        # Remove potential markdown and extra text
        response_text = re.sub(r'```json\s*', '', response_text, flags=re.IGNORECASE)
        response_text = re.sub(r'```\s*', '', response_text)
        match = re.search(r'({[\s\S]*})', response_text)
        if match:
            try:
                json_str = match.group(1)
                result = json.loads(json_str)
                print("Successfully extracted JSON from text.")
                return result
            except json.JSONDecodeError:
                print("JSON extraction from text failed.")
        
        # 3. 尝试修复常见的JSON错误
        fixed_response = self._fix_json_errors(response_text)
        try:
            result = json.loads(fixed_response)
            print("Successfully parsed after auto-fixing JSON.")
            return result
        except json.JSONDecodeError:
            print("JSON auto-fixing failed.")
        
        # 4. 尝试自然语言解析
        print("Attempting natural language parsing...")
        
        # 尝试从文本中提取verdict
        verdict = 0  # 默认为非冒犯性
        explanation = "Unable to determine verdict due to processing error."
        
        # 4.1 尝试查找数字verdict
        verdict_match = re.search(r'(verdict|判断|结论|判定|classification)[\s:]*([01])', 
                                response_text, re.IGNORECASE)
        if verdict_match:
            try:
                verdict = int(verdict_match.group(2))
            except:
                pass
        
        # 4.2 尝试通过关键词判断
        elif re.search(r'(offensive|冒犯|攻击性|侮辱|辱骂|粗鲁|不礼貌|歧视|仇恨|威胁)', response_text, re.IGNORECASE):
            # 检查是否有否定词
            if not re.search(r'(not offensive|non-offensive|非冒犯|没有冒犯|不含冒犯|不具攻击性)', 
                          response_text, re.IGNORECASE):
                verdict = 1
        
        # 4.3 尝试提取解释
        explanation_match = re.search(r'(explanation|解释|原因|说明)[\s:]*(.*?)(?:\n\n|$)', 
                                   response_text, re.DOTALL|re.IGNORECASE)
        if explanation_match:
            explanation = explanation_match.group(2).strip()[:200]  # 限制长度
        else:
            # 如果没有明确的explanation，取一段有意义的文本
            # 清理常见的无关文本
            cleaned_text = re.sub(r'(```json|```|verdict:|explanation:)', '', response_text, 
                               flags=re.IGNORECASE)
            # 去除空行和多余空格
            cleaned_text = re.sub(r'\n\s*\n', '\n', cleaned_text).strip()
            if cleaned_text:
                explanation = cleaned_text[:200] + "..."  # 限制长度
        
        print(f"Natural language parsing result: verdict={verdict}, explanation found: {len(explanation) > 0}")
        
        # 5. 构建结果对象
        if agent_type == "single_agent":
            return {
                "verdict": verdict,
                "explanation": explanation
            }
        elif agent_type == "retrieval_agent":
            return {
                "verdict": verdict,
                "explanation": explanation,
                "reference_usage": "Reference examples could not be properly analyzed due to parsing error."
            }
        else:
            return {"error": f"Unknown agent type: {agent_type}"}
            
    def _get_default_structure(self, agent_type, original_text=None):
        """
        获取默认响应结构
        
        Args:
            agent_type: 代理类型
            original_text: 原始文本
            
        Returns:
            默认响应结构
        """
        if agent_type == "single_agent":
            return {
                "verdict": 0,  # Default to non-offensive
                "explanation": "Unable to determine verdict due to processing error."
            }
        elif agent_type == "retrieval_agent":
            return {
                "verdict": 0,  # Default to non-offensive
                "explanation": "Unable to determine verdict due to processing error.",
                "reference_usage": "Could not use references due to technical error."
            }
        else:
            return {"error": f"Unknown agent type: {agent_type}"}

class SingleAgentDetector(OffensiveSpeechDetector):
    """单智能体冒犯性言论检测器"""
    def __init__(self, model="gpt-3.5-turbo-0125", provider="api", ollama_base_url="http://localhost:11434", local_model_path=None):
        super().__init__(model, provider, ollama_base_url, local_model_path)

    def detect(self, text):
        """使用单一LLM检测冒犯性言论"""
        import time
        start_time = time.time()

        prompt = self.create_prompt("single_agent")
        response_text = self._call_model_with_retry(prompt, text)
        parsed_result = self._parse_json_safely(response_text, "single_agent", text)

        # 添加处理时间
        processing_time = time.time() - start_time
        parsed_result['processing_time'] = processing_time

        # 添加原始模型输出到结果中
        parsed_result['raw_model_output'] = response_text if response_text else ""

        return parsed_result

class VectorDatabaseManager:
    """向量数据库管理器，负责创建和管理ChromaDB向量数据库"""
    
    def __init__(self, base_path="./vector_db"):
        """
        初始化向量数据库管理器
        
        Args:
            base_path: 向量数据库存储的基础路径
        """
        self.base_path = base_path
        os.makedirs(base_path, exist_ok=True)
        
        # 初始化OpenAI客户端用于创建嵌入
        # 初始化与主客户端相同配置的嵌入客户端
        self.embedding_client = OpenAI(
            base_url="https://yunwu.ai/v1",
            api_key="sk-I1h67Bmh1AlgvSOZ0yVfiA3zuri0IkdH3na7FQhRqqRUA7uz",
            timeout=60
        )
        
        # 初始化向量数据库客户端
        try:
            self.chroma_client = chromadb.PersistentClient(
                path=base_path,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True,
                    is_persistent=True
                )
            )
            print(f"成功初始化ChromaDB客户端，路径: {base_path}")
        except Exception as e:
            print(f"初始化ChromaDB客户端失败: {str(e)}")
            # 重新尝试初始化客户端，但使用更简单的配置
            try:
                self.chroma_client = chromadb.PersistentClient(path=base_path)
                print("使用简化配置重新初始化ChromaDB客户端成功")
            except Exception as e2:
                print(f"重新初始化ChromaDB客户端仍然失败: {str(e2)}")
                raise e2
    
    def dataset_has_vectordb(self, dataset_name):
        """
        检查数据集是否已经有向量数据库
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            布尔值，表示是否存在
        """
        try:
            # 先检查collection是否存在于数据库中
            collections = self.chroma_client.list_collections()
            if dataset_name not in [c.name for c in collections]:
                print(f"向量数据库 '{dataset_name}' 不存在")
                return False
            
            # 尝试获取集合
            self.chroma_client.get_collection(name=dataset_name)
            print(f"向量数据库 '{dataset_name}' 存在并可访问")
            return True
        except Exception as e:
            print(f"检查向量数据库 '{dataset_name}' 时出错: {str(e)}")
            return False
    
    def create_embeddings(self, texts):
        """
        为文本创建嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        try:
            # OpenAI API recommends batch sizes of 100 or less
            batch_size = 100
            all_embeddings = []
            
            # Determine if a progress bar is needed (for larger jobs)
            show_progress = len(texts) > batch_size
            
            batch_iterator = range(0, len(texts), batch_size)
            if show_progress:
                batch_iterator = tqdm(batch_iterator, desc="Generating Embeddings")

            for i in batch_iterator:
                batch_texts = texts[i:i + batch_size]
                response = self.embedding_client.embeddings.create(
                    input=batch_texts,
                    model="text-embedding-ada-002"
                )
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
                
                # Add a short delay to avoid API rate limiting when batching
                if len(texts) > 1 and i + batch_size < len(texts):
                    time.sleep(0.5)
            
            return all_embeddings
        except Exception as e:
            print(f"Error creating embeddings: {str(e)}")
            raise
    
    def create_vectordb_for_dataset(self, dataset_name, texts, labels, metadata=None):
        """
        为数据集创建向量数据库
        
        Args:
            dataset_name: 数据集名称
            texts: 文本列表
            labels: 标签列表
            metadata: 元数据列表
            
        Returns:
            成功创建的布尔值
        """
        if not texts or len(texts) == 0:
            print("No texts provided for embedding")
            return False
        
        if self.dataset_has_vectordb(dataset_name):
            print(f"Vector database for dataset {dataset_name} already exists")
            return True
        
        try:
            # 创建collection
            print(f"Creating vector database for dataset {dataset_name}...")
            collection = self.chroma_client.create_collection(name=dataset_name)
            
            # 生成嵌入
            print("Generating text embeddings...")
            embeddings = self.create_embeddings(texts)
            
            # 准备metadata
            print("Preparing metadata...")
            if metadata is None:
                metadata = [{"label": label} for label in labels]
            else:
                for i, meta in enumerate(metadata):
                    meta["label"] = labels[i]
            
            # 添加数据到collection
            print("Adding data to vector database...")
            # 分批添加数据以提高性能和显示进度
            batch_size = 500
            for i in tqdm(range(0, len(texts), batch_size), desc="Adding to VectorDB"):
                end_idx = min(i + batch_size, len(texts))
                batch_texts = texts[i:end_idx]
                batch_embeddings = embeddings[i:end_idx]
                batch_metadata = metadata[i:end_idx]
                # 使用数据集名称和数字索引作为ID，保持固定格式
                batch_ids = [f"{dataset_name}_{j}" for j in range(i, end_idx)]
                
                collection.add(
                    embeddings=batch_embeddings,
                    documents=batch_texts,
                    metadatas=batch_metadata,
                    ids=batch_ids
                )
            
            print(f"Successfully created vector database for {dataset_name} with {len(texts)} entries.")
            return True
        except Exception as e:
            print(f"Error creating vector database for {dataset_name}: {str(e)}")
            return False
    
    def query_similar_texts(self, dataset_name, query_text, n_results=5, distance_threshold=0.8):
        """
        查询相似文本
        
        Args:
            dataset_name: 数据集名称
            query_text: 查询文本
            n_results: 返回结果数量
            distance_threshold: 仅返回距离小于此阈值的样本
            
        Returns:
            相似文本及其元数据列表
        """
        if not self.dataset_has_vectordb(dataset_name):
            print(f"Vector database for dataset {dataset_name} does not exist")
            return []
        
        try:
            collection = self.chroma_client.get_collection(name=dataset_name)
            
            # 生成查询文本的嵌入向量
            query_embedding = self.create_embeddings([query_text])[0]
            
            # 查询相似文本
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                include=["documents", "metadatas", "distances"]
            )
            
            # 格式化并过滤结果
            formatted_results = []
            if results and results["documents"]:
                for i in range(len(results["documents"][0])):
                    distance = results["distances"][0][i]
                    if distance < distance_threshold:
                        formatted_results.append({
                            "text": results["documents"][0][i],
                            "metadata": results["metadatas"][0][i],
                            "distance": distance
                        })
            
            return formatted_results
        except Exception as e:
            print(f"Error querying vector database for dataset {dataset_name}: {str(e)}")
            return []

class RetrievalAugmentedDetector(OffensiveSpeechDetector):
    """使用检索增强的冒犯性言论检测器"""

    def __init__(self, model="gpt-3.5-turbo-0125", provider="api", ollama_base_url="http://localhost:11434", local_model_path=None,
                 vector_db_manager=None):
        super().__init__(model, provider, ollama_base_url, local_model_path)
        self.vector_db = vector_db_manager if vector_db_manager else VectorDatabaseManager()
        self.last_similar_texts = []
        self.retrieved_examples_prompt = ""

    def prepare_dataset_vectordb(self, dataset_name, train_data):
        """
        为数据集准备向量数据库
        
        Args:
            dataset_name: 数据集名称
            train_data: 训练数据
            
        Returns:
            布尔值，表示是否成功
        """
        # 检查是否已经存在向量数据库
        if self.vector_db.dataset_has_vectordb(dataset_name):
            print(f"数据集 {dataset_name} 的向量数据库已存在，跳过创建")
            return True
        
        print(f"准备为数据集 {dataset_name} 构建向量数据库...")
        print(f"数据集大小: {len(train_data)} 条记录")
        
        # 提取文本和标签
        print("提取文本和标签...")
        texts = [item["text"] for item in train_data]
        labels = [item["label"] for item in train_data]
        
        # 准备元数据
        print("准备元数据...")
        metadata = []
        for item in tqdm(train_data, desc="处理元数据"):
            # 复制除text和label以外的所有字段
            meta = {k: v for k, v in item.items() if k not in ["text", "label"]}
            metadata.append(meta)
        
        # 创建向量数据库
        print(f"开始构建 {dataset_name} 数据集的向量数据库...")
        result = self.vector_db.create_vectordb_for_dataset(dataset_name, texts, labels, metadata)
        
        if result:
            print(f"数据集 {dataset_name} 的向量数据库构建完成！")
        else:
            print(f"数据集 {dataset_name} 的向量数据库构建失败！")
            
        return result
    
    def detect(self, text, dataset_name):
        """通过检索增强检测冒-犯性言论"""
        import time
        start_time = time.time()

        try:
            self.last_similar_texts = self.vector_db.query_similar_texts(dataset_name, text, n_results=3)
        except Exception as e:
            logging.error(f"Error querying vector DB for dataset {dataset_name}: {e}")
            self.last_similar_texts = []

        if not self.last_similar_texts:
            # 如果没有相似文本，则退回到SingleAgent模式
            result = self._fallback_to_single_agent(text)
            result['processing_time'] = time.time() - start_time
            return result

        self.retrieved_examples_prompt = ""
        for i, item in enumerate(self.last_similar_texts):
            self.retrieved_examples_prompt += f'Example {i+1}: "{item["text"]}"\nVerdict: {item["metadata"]["label"]} ({"offensive" if item["metadata"]["label"] == 1 else "non-offensive"} speech)\n\n'

        prompt_template = self.create_prompt("retrieval_agent")
        # 正确地将检索到的示例替换到模板中，生成最终的系统提示
        system_prompt = prompt_template.replace("{{retrieved_examples}}", self.retrieved_examples_prompt)

        user_content = f"Please analyze the following text:\n\"\"\"{text}\"\"\""

        response_text = self._call_model_with_retry(system_prompt, user_content)
        parsed_result = self._parse_json_safely(response_text, "retrieval_agent", text)

        # 添加处理时间
        processing_time = time.time() - start_time
        parsed_result['processing_time'] = processing_time

        # 添加检索到的示例到结果中
        parsed_result['retrieved_examples_prompt'] = self.retrieved_examples_prompt

        # 添加原始模型输出到结果中
        parsed_result['raw_model_output'] = response_text if response_text else ""

        return parsed_result

    def _fallback_to_single_agent(self, text):
        """在没有检索结果时退回到单一智能体"""
        import time
        start_time = time.time()

        # logging.warning("No similar texts found, falling back to SingleAgent mode.")
        self.retrieved_examples_prompt = "No similar examples were found."

        # 使用SingleAgent的prompt和逻辑
        prompt = self.create_prompt("single_agent")
        response_text = self._call_model_with_retry(prompt, text)

        # 解析时仍标记为retrieval_agent以保持一致性，但内部逻辑是single_agent
        parsed_response = self._parse_json_safely(response_text, "retrieval_agent", text)
        parsed_response['reference_usage'] = 'Fallback to single agent mode due to no similar texts found.'

        # 添加处理时间
        processing_time = time.time() - start_time
        parsed_response['processing_time'] = processing_time
        
        # 添加原始模型输出
        parsed_response['raw_model_output'] = response_text if response_text else ""
        
        return parsed_response

class MultiAgentOrchestrator(OffensiveSpeechDetector):
    """
    多智能体编排系统，结合单一智能体和检索增强智能体
    """
    def __init__(self, model="gpt-3.5-turbo-0125", provider="api", ollama_base_url="http://localhost:11434", local_model_path=None, vector_db_manager=None, dataset_name=None):
        super().__init__(model, provider, ollama_base_url, local_model_path)

        self.single_agent = SingleAgentDetector(model=model, provider=provider, ollama_base_url=ollama_base_url, local_model_path=local_model_path)
        self.retrieval_agent = RetrievalAugmentedDetector(model=model, provider=provider, ollama_base_url=ollama_base_url, local_model_path=local_model_path, vector_db_manager=vector_db_manager)

        # 用于记录决策信息
        self.last_chosen_agent = None

    def detect(self, text, dataset_name):
        # 1. Get predictions from both agents
        single_agent_result = self.single_agent.detect(text)
        retrieval_agent_result = self.retrieval_agent.detect(text, dataset_name)

        # 2. Simple decision logic to replace RL
        single_verdict = single_agent_result.get('verdict', -1)
        retrieval_verdict = retrieval_agent_result.get('verdict', -1)
        has_similar = bool(self.retrieval_agent.last_similar_texts)

        # 确保verdict是0或1，如果不是则为-1
        single_verdict = single_verdict if single_verdict in [0, 1] else -1
        retrieval_verdict = retrieval_verdict if retrieval_verdict in [0, 1] else -1

        # 3. Decision logic:
        # - If both agents agree and have valid verdicts, use that result
        # - If they disagree and retrieval agent has similar examples, prefer retrieval agent
        # - Otherwise, use single agent result
        if single_verdict == retrieval_verdict and single_verdict in [0, 1]:
            # Both agents agree
            chosen_agent = 'consensus'
            final_result = single_agent_result.copy()  # Use single agent result as base
        elif has_similar and retrieval_verdict in [0, 1]:
            # Retrieval agent has context and valid result
            chosen_agent = 'retrieval'
            final_result = retrieval_agent_result.copy()
        else:
            # Default to single agent
            chosen_agent = 'single'
            final_result = single_agent_result.copy()

        # Store chosen agent for logging
        self.last_chosen_agent = chosen_agent

        # Combine results for logging
        final_result['raw_output_single'] = single_agent_result.get('raw_model_output', '')
        final_result['raw_output_retrieval'] = retrieval_agent_result.get('raw_model_output', '')

        # 如果存在旧的'raw_model_output'键，则移除，避免混淆
        if 'raw_model_output' in final_result:
            del final_result['raw_model_output']

        final_result['chosen_agent'] = chosen_agent
        final_result['single_agent_verdict'] = single_agent_result.get('verdict')
        final_result['retrieval_agent_verdict'] = retrieval_agent_result.get('verdict')
        final_result['retrieved_examples_prompt'] = self.retrieval_agent.retrieved_examples_prompt

        return final_result

    def get_decision_info(self):
        """
        获取最后一次决策的信息，用于日志记录
        """
        return {
            'chosen_agent': self.last_chosen_agent
        }


# ==================== 高级多智能体系统集成 ====================

@dataclass
class AdvancedSystemConfig:
    """高级系统配置"""
    system_type: str = "intelligent_coordination"  # 只支持智能协调系统
    enable_dynamic_weights: bool = True
    enable_performance_tracking: bool = True
    enable_online_learning: bool = True
    learning_rate: float = 0.001
    adaptation_threshold: float = 0.1
    confidence_threshold: float = 0.6
    weight_update_frequency: int = 10
    coordination_method: str = "dynamic_weighted"  # static_weighted, dynamic_weighted, consensus_based

    # 新增配置选项
    enable_caching: bool = True
    cache_size: int = 1000
    enable_detailed_logging: bool = True
    enable_moe_load_balancing: bool = True
    nas_search_iterations: int = 50
    nas_population_size: int = 20
    hybrid_fusion_method: str = "weighted_average"  # weighted_average, voting, confidence_based
    expert_specialization_threshold: float = 0.7
    architecture_mutation_rate: float = 0.1
    performance_window_size: int = 100


class DynamicWeightAllocator:
    """动态权重分配器 - 支持随机初始化和快速自适应学习"""

    def __init__(self, config: AdvancedSystemConfig):
        self.config = config

        # 随机初始化权重（而非固定权重）
        import random
        random.seed(42)  # 为了可重现性
        initial_weights = [random.uniform(0.2, 0.5) for _ in range(3)]
        total = sum(initial_weights)
        self.current_weights = {
            "toxigen_roberta": initial_weights[0] / total,  # ToxiGenRoBERTa智能体
            "llm_single": initial_weights[1] / total,
            "llm_retrieval": initial_weights[2] / total
        }
        print(f"🎲 随机初始化权重: {self.current_weights}")

        # 性能历史记录（统一简化版本）
        self.agent_performance_history = {
            "toxigen_roberta": {"accuracy": [], "response_time": []},  # ToxiGenRoBERTa智能体
            "llm_single": {"accuracy": [], "response_time": []},
            "llm_retrieval": {"accuracy": [], "response_time": []}
        }

        # 在线学习相关
        self.learning_rate = config.learning_rate
        self.momentum = 0.8  # 降低动量系数，增加响应性
        self.weight_momentum = {"toxigen_roberta": 0.0, "llm_single": 0.0, "llm_retrieval": 0.0}

        # 自适应学习率 - 为新算法调整
        self.adaptive_lr = 0.02  # 新算法使用更合适的学习率
        self.lr_decay = 0.95
        self.lr_increase = 1.05

        # 性能窗口
        self.performance_window = config.performance_window_size
        self.prediction_count = 0

        # 文本特征缓存
        self.text_features_cache = {}

        # 权重更新历史
        self.weight_history = []

    def extract_text_features(self, text: str) -> Dict[str, float]:
        """提取文本特征"""
        if text in self.text_features_cache:
            return self.text_features_cache[text]

        features = {
            "length": len(text) / 100.0,  # 归一化长度
            "word_count": len(text.split()) / 50.0,  # 归一化词数
            "has_chinese": 1.0 if any('\u4e00' <= char <= '\u9fff' for char in text) else 0.0,
            "has_english": 1.0 if any(char.isalpha() and ord(char) < 128 for char in text) else 0.0,
            "sentiment_intensity": abs(len([w for w in text.split() if w.lower() in ['hate', 'love', 'angry', 'happy']]) / max(len(text.split()), 1)),
            "complexity": len(set(text.split())) / max(len(text.split()), 1)  # 词汇多样性
        }

        self.text_features_cache[text] = features
        return features

    def predict_weights(self, text: str, dataset_name: str = None) -> Dict[str, float]:
        """基于当前权重、文本特征和历史性能动态预测权重"""
        features = self.extract_text_features(text)

        # 使用当前学习到的权重作为基础
        base_weights = self.current_weights.copy()

        # 基于文本特征进行微调（幅度较小，让学习主导）
        feature_adjustment = 0.05  # 减小特征调整幅度

        if features["has_chinese"] > 0.5:
            base_weights["llm_single"] += feature_adjustment * 0.5
            base_weights["toxigen_roberta"] -= feature_adjustment * 0.25
            base_weights["llm_retrieval"] -= feature_adjustment * 0.25

        if features["complexity"] > 0.7:
            base_weights["llm_retrieval"] += feature_adjustment * 0.6
            base_weights["toxigen_roberta"] -= feature_adjustment * 0.3
            base_weights["llm_single"] -= feature_adjustment * 0.3

        if features["length"] > 0.8:
            base_weights["llm_retrieval"] += feature_adjustment * 0.4
            base_weights["toxigen_roberta"] -= feature_adjustment * 0.2
            base_weights["llm_single"] -= feature_adjustment * 0.2

        # 基于最近性能进行调整（统一只使用准确率）
        performance_adjustment = 0.1
        for agent_name in base_weights:
            if len(self.agent_performance_history[agent_name]["accuracy"]) >= 3:
                # 使用最近3次的准确率
                recent_accuracy = self.agent_performance_history[agent_name]["accuracy"][-3:]
                avg_accuracy = sum(recent_accuracy) / len(recent_accuracy)

                # 根据准确率调整权重
                if avg_accuracy > 0.75:
                    base_weights[agent_name] += performance_adjustment * (avg_accuracy - 0.5)
                elif avg_accuracy < 0.45:
                    base_weights[agent_name] -= performance_adjustment * (0.5 - avg_accuracy)

        # 确保权重在合理范围内
        for agent_name in base_weights:
            base_weights[agent_name] = max(0.05, min(0.8, base_weights[agent_name]))

        # 归一化权重
        total_weight = sum(base_weights.values())
        normalized_weights = {k: v / total_weight for k, v in base_weights.items()}

        return normalized_weights

    def update_performance(self, agent_name: str, accuracy: float, response_time: float = 0.0):
        """更新智能体性能历史（统一不使用置信度）"""
        if agent_name in self.agent_performance_history:
            self.agent_performance_history[agent_name]["accuracy"].append(accuracy)
            self.agent_performance_history[agent_name]["response_time"].append(response_time)

            # 保持最近100次记录
            for metric in ["accuracy", "response_time"]:
                if len(self.agent_performance_history[agent_name][metric]) > 100:
                    self.agent_performance_history[agent_name][metric] = self.agent_performance_history[agent_name][metric][-100:]

    def online_learning_update(self, agent_results: Dict[str, Dict], true_label: int, predicted_label: int):
        """在线学习权重更新 - 核心学习算法"""
        self.prediction_count += 1

        # 计算每个智能体的准确性
        agent_accuracies = {}

        for agent_name, result in agent_results.items():
            agent_verdict = result.get("verdict", -1)

            if agent_verdict in [0, 1]:
                # 计算该智能体的准确性
                agent_accuracy = 1.0 if agent_verdict == true_label else 0.0
                agent_accuracies[agent_name] = agent_accuracy

                # 更新性能历史
                response_time = result.get("processing_time", 0.0)
                self.update_performance(agent_name, agent_accuracy, response_time)

        # 如果有足够的数据，进行权重更新
        if len(agent_accuracies) >= 2 and self.prediction_count >= 3:
            self._adaptive_weight_update(agent_accuracies, predicted_label == true_label)

    def _adaptive_weight_update(self, agent_accuracies: Dict[str, float], overall_correct: bool):
        """新的对称奖励/惩罚权重更新算法"""
        # 使用新的对称奖励/惩罚机制
        for agent_name in self.current_weights:
            if agent_name in agent_accuracies:
                # 计算奖励：预测正确为1，错误为0
                reward = agent_accuracies[agent_name]  # 已经是0或1

                # 对称的权重变化：正确+0.5η，错误-0.5η
                change = self.adaptive_lr * (reward - 0.5)

                # 直接更新权重（暂时不使用动量，简化算法）
                self.current_weights[agent_name] += change

                # 确保权重在合理范围内
                self.current_weights[agent_name] = max(0.05, min(0.8, self.current_weights[agent_name]))
            else:
                # 没有有效预测的智能体权重不变
                pass

        # 归一化权重
        total_weight = sum(self.current_weights.values())
        if total_weight > 0:
            for agent_name in self.current_weights:
                self.current_weights[agent_name] /= total_weight

        # 简化的学习率调整：基于整体性能
        if overall_correct:
            self.adaptive_lr = min(self.config.learning_rate * 2, self.adaptive_lr * 1.05)
        else:
            self.adaptive_lr = max(self.config.learning_rate * 0.5, self.adaptive_lr * 0.95)

        # 记录权重历史
        self.weight_history.append({
            "step": self.prediction_count,
            "weights": self.current_weights.copy(),
            "learning_rate": self.adaptive_lr,
            "overall_correct": overall_correct,
            "agent_accuracies": agent_accuracies.copy()
        })

        # 保持历史记录在合理范围内
        if len(self.weight_history) > 1000:
            self.weight_history = self.weight_history[-1000:]

        # 每10次预测打印一次权重更新信息
        if self.prediction_count % 10 == 0:
            print(f"🔄 权重更新 (第{self.prediction_count}次): {self.current_weights}")
            print(f"📈 当前学习率: {self.adaptive_lr:.4f}")
            print(f"🎯 智能体准确率: {agent_accuracies}")

    def get_learning_stats(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        stats = {
            "prediction_count": self.prediction_count,
            "current_weights": self.current_weights.copy(),
            "adaptive_learning_rate": self.adaptive_lr,
            "weight_momentum": self.weight_momentum.copy()
        }

        # 计算每个智能体的平均性能
        for agent_name in self.agent_performance_history:
            if self.agent_performance_history[agent_name]["accuracy"]:
                recent_accuracy = self.agent_performance_history[agent_name]["accuracy"][-10:]
                recent_confidence = self.agent_performance_history[agent_name]["confidence"][-10:]

                stats[f"{agent_name}_avg_accuracy"] = sum(recent_accuracy) / len(recent_accuracy)
                stats[f"{agent_name}_avg_confidence"] = sum(recent_confidence) / len(recent_confidence)

        return stats


class PerformanceTracker:
    """性能追踪器"""

    def __init__(self):
        self.prediction_history = []
        self.accuracy_history = []
        self.coordination_stats = {
            "total_predictions": 0,
            "correct_predictions": 0,
            "avg_confidence": 0.0,
            "avg_processing_time": 0.0
        }

    def record_prediction(self, text: str, prediction: int, confidence: float,
                         true_label: Optional[int] = None, processing_time: float = 0.0):
        """记录预测结果"""
        record = {
            "text": text,
            "prediction": prediction,
            "confidence": confidence,
            "true_label": true_label,
            "processing_time": processing_time,
            "timestamp": time.time()
        }

        self.prediction_history.append(record)
        self.coordination_stats["total_predictions"] += 1

        if true_label is not None:
            is_correct = (prediction == true_label)
            self.accuracy_history.append(is_correct)
            if is_correct:
                self.coordination_stats["correct_predictions"] += 1

        # 更新统计信息
        self._update_stats()

    def _update_stats(self):
        """更新统计信息"""
        if self.prediction_history:
            confidences = [r["confidence"] for r in self.prediction_history if r["confidence"] is not None]
            processing_times = [r["processing_time"] for r in self.prediction_history if r["processing_time"] > 0]

            if confidences:
                self.coordination_stats["avg_confidence"] = sum(confidences) / len(confidences)
            if processing_times:
                self.coordination_stats["avg_processing_time"] = sum(processing_times) / len(processing_times)

    def get_recent_accuracy(self, window_size: int = 20) -> float:
        """获取最近的准确率"""
        if len(self.accuracy_history) < window_size:
            return sum(self.accuracy_history) / max(len(self.accuracy_history), 1)
        else:
            recent_accuracy = self.accuracy_history[-window_size:]
            return sum(recent_accuracy) / len(recent_accuracy)


class IntelligentCoordinationSystem(OffensiveSpeechDetector):
    """智能协调系统 - 集成动态权重分配和性能追踪"""

    def __init__(self, model="gpt-3.5-turbo-0125", provider="api", ollama_base_url="http://localhost:11434",
                 local_model_path=None, vector_db_manager=None, dataset_name=None, config: AdvancedSystemConfig = None):
        super().__init__(model, provider, ollama_base_url, local_model_path)

        self.config = config or AdvancedSystemConfig()
        self.dataset_name = dataset_name

        # 初始化ToxiGenRoBERTa智能体
        try:
            from core_modules.agents import ToxiGenRoBERTaAgent, ToxiGenRoBERTaConfig
            # 使用默认配置
            toxigen_config = ToxiGenRoBERTaConfig()
            self.toxigen_agent = ToxiGenRoBERTaAgent(toxigen_config)
            print("✅ 成功加载ToxiGenRoBERTa智能体")
        except Exception as e:
            print(f"⚠️ ToxiGenRoBERTa智能体加载失败，使用LLM替代: {e}")
            self.toxigen_agent = SingleAgentDetector(model=model, provider=provider, ollama_base_url=ollama_base_url, local_model_path=local_model_path)

        # 为了避免重复加载本地模型，我们创建一个共享的LocalModelClient实例
        if provider == "local" and local_model_path:
            print(f"🔧 为本地模型创建共享客户端: {local_model_path}")
            self._shared_local_client = LocalModelClient(model_path=local_model_path)
        else:
            self._shared_local_client = None

        self.llm_single_agent = SingleAgentDetector(model=model, provider=provider, ollama_base_url=ollama_base_url, local_model_path=local_model_path)
        self.llm_retrieval_agent = RetrievalAugmentedDetector(model=model, provider=provider, ollama_base_url=ollama_base_url, local_model_path=local_model_path, vector_db_manager=vector_db_manager)

        # 如果使用本地模型，将共享客户端传递给智能体
        if self._shared_local_client:
            self.llm_single_agent._shared_local_client = self._shared_local_client
            self.llm_retrieval_agent._shared_local_client = self._shared_local_client

        # 初始化高级组件
        self.weight_allocator = DynamicWeightAllocator(self.config)
        self.performance_tracker = PerformanceTracker()

        # 协调统计
        self.coordination_stats = {
            "total_predictions": 0,
            "dynamic_weight_predictions": 0,
            "consensus_predictions": 0,
            "avg_coordination_time": 0.0,
            "system_type": "intelligent_coordination"
        }

        self.last_coordination_info = {}

    def detect(self, text: str, dataset_name: str = None, true_label: Optional[int] = None) -> Dict[str, Any]:
        """智能检测接口"""
        start_time = time.time()

        try:
            # 1. 获取所有智能体的预测
            agent_results = self._get_agent_predictions(text, dataset_name)

            # 2. 动态权重分配
            if self.config.enable_dynamic_weights:
                weights = self.weight_allocator.predict_weights(text, dataset_name or self.dataset_name)
                coordination_method = "dynamic_weighted"
                self.coordination_stats["dynamic_weight_predictions"] += 1
            else:
                # 即使不启用动态权重，也使用当前学习到的权重
                weights = self.weight_allocator.current_weights.copy()
                coordination_method = "learned_static"

            # 3. 加权协调决策
            coordinated_result = self._weighted_coordination(agent_results, weights)

            # 4. 记录性能数据
            if self.config.enable_performance_tracking:
                processing_time = time.time() - start_time
                self.performance_tracker.record_prediction(
                    text, coordinated_result.get("verdict", -1),
                    coordinated_result.get("confidence", 0.0),
                    true_label, processing_time
                )

            # 5. 在线学习更新（使用新的学习算法）
            if self.config.enable_online_learning and true_label is not None:
                predicted_label = coordinated_result.get("verdict", -1)
                if predicted_label in [0, 1]:
                    self.weight_allocator.online_learning_update(
                        agent_results, true_label, predicted_label
                    )

            # 6. 构建最终结果
            coordination_time = time.time() - start_time
            self.coordination_stats["total_predictions"] += 1
            self.coordination_stats["avg_coordination_time"] = (
                (self.coordination_stats["avg_coordination_time"] * (self.coordination_stats["total_predictions"] - 1) + coordination_time)
                / self.coordination_stats["total_predictions"]
            )

            # 6. 保存协调信息
            self.last_coordination_info = {
                "coordination_method": coordination_method,
                "agent_weights": weights,
                "agent_results": agent_results,
                "coordination_time": coordination_time,
                "system_type": "intelligent_coordination"
            }

            # 7. 扩展结果信息
            coordinated_result.update({
                "coordination_method": coordination_method,
                "agent_weights": weights,
                "coordination_time": coordination_time,
                "system_type": "intelligent_coordination",
                "performance_stats": self.coordination_stats.copy()
            })

            return coordinated_result

        except Exception as e:
            print(f"Error in intelligent coordination: {e}")
            # 降级到简单的ToxiGenRoBERTa检测
            return self.toxigen_agent.detect(text)

    def _get_agent_predictions(self, text: str, dataset_name: str = None) -> Dict[str, Dict[str, Any]]:
        """获取所有智能体的预测结果 - 真正的并行调用"""
        import concurrent.futures
        import time

        agent_results = {}
        start_time = time.time()

        def call_toxigen_agent():
            """调用ToxiGenRoBERTa智能体"""
            try:
                agent_start = time.time()
                if hasattr(self.toxigen_agent, 'detect'):
                    # 如果是ToxiGenRoBERTa智能体
                    if 'ToxiGenRoBERTaAgent' in str(type(self.toxigen_agent)):
                        result = self.toxigen_agent.detect(text, method="classification")
                    else:
                        result = self.toxigen_agent.detect(text)
                else:
                    result = {"verdict": -1, "confidence": 0.0, "reasoning": "No detect method"}

                # 确保有处理时间
                if 'processing_time' not in result:
                    result['processing_time'] = time.time() - agent_start

                return {
                    "verdict": result.get("verdict", -1),
                    "confidence": result.get("confidence", 0.5),
                    "reasoning": result.get("reasoning", result.get("explanation", "")),
                    "processing_time": result.get("processing_time", 0.0)
                }
            except Exception as e:
                print(f"DeBERTa agent error: {e}")
                return {"verdict": -1, "confidence": 0.0, "reasoning": f"Error: {str(e)}", "processing_time": 0.0}

        def call_llm_single_agent():
            """调用LLM单智能体"""
            try:
                result = self.llm_single_agent.detect(text)
                return {
                    "verdict": result.get("verdict", -1),
                    "reasoning": result.get("reasoning", result.get("explanation", "")),
                    "processing_time": result.get("processing_time", 0.0)
                }
            except Exception as e:
                print(f"LLM single agent error: {e}")
                return {"verdict": -1, "reasoning": f"Error: {str(e)}", "processing_time": 0.0}

        def call_llm_retrieval_agent():
            """调用LLM检索增强智能体"""
            try:
                result = self.llm_retrieval_agent.detect(text, dataset_name or self.dataset_name)
                return {
                    "verdict": result.get("verdict", -1),
                    "reasoning": result.get("reasoning", result.get("explanation", "")),
                    "processing_time": result.get("processing_time", 0.0),
                    "retrieved_examples": result.get("retrieved_examples_prompt", "")
                }
            except Exception as e:
                print(f"LLM retrieval agent error: {e}")
                return {"verdict": -1, "reasoning": f"Error: {str(e)}", "processing_time": 0.0}

        # 🚀 真正的并行执行
        print("🚀 开始并行调用三个智能体...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # 提交所有任务
            future_toxigen = executor.submit(call_toxigen_agent)
            future_llm_single = executor.submit(call_llm_single_agent)
            future_llm_retrieval = executor.submit(call_llm_retrieval_agent)

            # 等待所有任务完成
            agent_results["toxigen_roberta"] = future_toxigen.result()
            agent_results["llm_single"] = future_llm_single.result()
            agent_results["llm_retrieval"] = future_llm_retrieval.result()

        total_time = time.time() - start_time
        print(f"✅ 并行调用完成，总耗时: {total_time:.3f}秒")
        print(f"   ToxiGenRoBERTa: {agent_results['toxigen_roberta']['processing_time']:.3f}s")
        print(f"   LLM单智能体: {agent_results['llm_single']['processing_time']:.3f}s")
        print(f"   LLM检索增强: {agent_results['llm_retrieval']['processing_time']:.3f}s")

        return agent_results

    def _weighted_coordination(self, agent_results: Dict[str, Dict[str, Any]], weights: Dict[str, float]) -> Dict[str, Any]:
        """纯粹的加权协调决策 - 基于在线学习的权重分配"""
        valid_agents = {k: v for k, v in agent_results.items() if v["verdict"] in [0, 1]}

        if not valid_agents:
            # 没有有效预测，返回默认结果
            return {
                "verdict": 0,
                "confidence": 0.5,
                "reasoning": "No valid predictions from agents",
                "chosen_agent": "none",
                "agent_consensus": "no_valid_predictions"
            }

        # 计算基于学习权重的加权预测
        weighted_prediction = 0.0
        total_weight = 0.0

        for agent_name, result in valid_agents.items():
            agent_weight = weights.get(agent_name, 0.0)
            weighted_prediction += result["verdict"] * agent_weight
            total_weight += agent_weight

        if total_weight > 0:
            weighted_prediction /= total_weight

        # 最终决策
        final_verdict = 1 if weighted_prediction >= 0.5 else 0

        # 确定共识状态
        verdicts = [result["verdict"] for result in valid_agents.values()]
        if len(set(verdicts)) == 1:
            consensus = "unanimous"
        elif len(valid_agents) >= 2 and sum(verdicts) / len(verdicts) >= 0.7:
            consensus = "strong_majority"
        elif len(valid_agents) >= 2 and sum(verdicts) / len(verdicts) >= 0.5:
            consensus = "weak_majority"
        else:
            consensus = "no_consensus"

        # 选择主导智能体
        if consensus == "unanimous":
            chosen_agent = "consensus"
        else:
            # 选择权重最高且预测正确的智能体
            best_agent = max(valid_agents.keys(), key=lambda x: weights.get(x, 0.0))
            chosen_agent = best_agent

        # 构建推理说明
        reasoning_parts = []
        for agent_name, result in valid_agents.items():
            agent_weight = weights.get(agent_name, 0.0)
            reasoning_parts.append(f"{agent_name}(权重:{agent_weight:.3f}, 预测:{result['verdict']})")

        # 基于共识和权重分布计算置信度
        # 1. 共识程度：一致性越高，置信度越高
        verdicts = [result["verdict"] for result in valid_agents.values()]
        consensus_score = 1.0 if len(set(verdicts)) == 1 else (max(verdicts.count(0), verdicts.count(1)) / len(verdicts))

        # 2. 权重分布：权重越集中，置信度越高
        max_weight = max(weights.values()) if weights else 0.5
        weight_concentration = max_weight

        # 3. 预测确定性：离0.5越远，置信度越高
        prediction_certainty = abs(weighted_prediction - 0.5) + 0.5

        # 综合计算最终置信度
        final_confidence = 0.4 * consensus_score + 0.3 * weight_concentration + 0.3 * prediction_certainty
        final_confidence = max(0.5, min(0.99, final_confidence))  # 限制在合理范围内

        reasoning = f"在线学习协调决策: {'; '.join(reasoning_parts)}. 最终预测: {final_verdict}"

        return {
            "verdict": final_verdict,
            "confidence": final_confidence,
            "reasoning": reasoning,
            "chosen_agent": chosen_agent,
            "agent_consensus": consensus,
            "valid_agents_count": len(valid_agents),
            "total_agents_count": len(agent_results),
            "learned_weights": weights,  # 记录学习到的权重
            "agent_results": agent_results
        }



    def get_decision_info(self):
        """获取最后一次决策的信息，用于日志记录"""
        return self.last_coordination_info

    def get_learning_stats(self):
        """获取学习统计信息"""
        stats = self.weight_allocator.get_learning_stats()
        stats.update({
            "coordination_stats": self.coordination_stats.copy(),
            "system_type": "intelligent_coordination_with_adaptive_learning"
        })
        return stats




# ==================== 系统工厂函数 ====================

def create_advanced_system(system_type: str, model="gpt-3.5-turbo-0125", provider="api",
                          ollama_base_url="http://localhost:11434", local_model_path=None,
                          vector_db_manager=None, dataset_name=None, config: AdvancedSystemConfig = None):
    """创建高级系统的工厂函数"""

    if config is None:
        config = AdvancedSystemConfig(system_type=system_type)

    # 只支持智能协调系统
    return IntelligentCoordinationSystem(
        model=model, provider=provider, ollama_base_url=ollama_base_url,
        local_model_path=local_model_path, vector_db_manager=vector_db_manager,
        dataset_name=dataset_name, config=config
    )