#!/usr/bin/env python3
"""
脚本用于部署和测试两个模型：
1. twitter-roberta-base-offensive - 冒犯性语言检测
2. twitter-roberta-base-sentiment-latest - 情绪分析

作者: AI Assistant
日期: 2025-07-31
"""

import os
import sys
import numpy as np
from scipy.special import softmax
import csv
import urllib.request
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
    AutoConfig,
    pipeline
)

class ModelDeploymentTester:
    def __init__(self):
        """初始化模型部署测试器"""
        self.offensive_model_path = r"D:\models\twitter-roberta-base-offensive"
        self.sentiment_model_path = r"D:\models\twitter-roberta-base-sentiment-latest"
        
        # 模型组件
        self.offensive_model = None
        self.offensive_tokenizer = None
        self.offensive_labels = []
        
        self.sentiment_model = None
        self.sentiment_tokenizer = None
        self.sentiment_config = None
        self.sentiment_pipeline = None
    
    def preprocess(self, text):
        """
        预处理文本（用户名和链接占位符）
        """
        new_text = []
        for t in text.split(" "):
            t = '@user' if t.startswith('@') and len(t) > 1 else t
            t = 'http' if t.startswith('http') else t
            new_text.append(t)
        return " ".join(new_text)
    
    def load_offensive_model(self):
        """加载冒犯性语言检测模型"""
        print("正在加载冒犯性语言检测模型...")
        
        try:
            # 检查模型路径是否存在
            if not os.path.exists(self.offensive_model_path):
                print(f"错误: 模型路径不存在: {self.offensive_model_path}")
                return False
            
            # 加载tokenizer和模型
            self.offensive_tokenizer = AutoTokenizer.from_pretrained(self.offensive_model_path)
            self.offensive_model = AutoModelForSequenceClassification.from_pretrained(self.offensive_model_path)
            
            # 下载标签映射
            task = 'offensive'
            mapping_link = f"https://raw.githubusercontent.com/cardiffnlp/tweeteval/main/datasets/{task}/mapping.txt"
            
            try:
                with urllib.request.urlopen(mapping_link) as f:
                    html = f.read().decode('utf-8').split("\n")
                    csvreader = csv.reader(html, delimiter='\t')
                    self.offensive_labels = [row[1] for row in csvreader if len(row) > 1]
            except Exception as e:
                print(f"警告: 无法下载标签映射，使用默认标签: {e}")
                self.offensive_labels = ['not-offensive', 'offensive']
            
            print("✓ 冒犯性语言检测模型加载成功")
            return True
            
        except Exception as e:
            print(f"错误: 加载冒犯性语言检测模型失败: {e}")
            return False
    
    def load_sentiment_model(self):
        """加载情绪分析模型"""
        print("正在加载情绪分析模型...")
        
        try:
            # 检查模型路径是否存在
            if not os.path.exists(self.sentiment_model_path):
                print(f"错误: 模型路径不存在: {self.sentiment_model_path}")
                return False
            
            # 加载tokenizer、配置和模型
            self.sentiment_tokenizer = AutoTokenizer.from_pretrained(self.sentiment_model_path)
            self.sentiment_config = AutoConfig.from_pretrained(self.sentiment_model_path)
            self.sentiment_model = AutoModelForSequenceClassification.from_pretrained(self.sentiment_model_path)
            
            # 创建pipeline
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis", 
                model=self.sentiment_model_path, 
                tokenizer=self.sentiment_model_path
            )
            
            print("✓ 情绪分析模型加载成功")
            return True
            
        except Exception as e:
            print(f"错误: 加载情绪分析模型失败: {e}")
            return False
    
    def test_offensive_model(self, text):
        """测试冒犯性语言检测模型"""
        if self.offensive_model is None or self.offensive_tokenizer is None:
            print("错误: 冒犯性语言检测模型未加载")
            return None
        
        try:
            # 预处理文本
            processed_text = self.preprocess(text)
            
            # 编码输入
            encoded_input = self.offensive_tokenizer(processed_text, return_tensors='pt')
            
            # 模型推理
            output = self.offensive_model(**encoded_input)
            scores = output[0][0].detach().numpy()
            scores = softmax(scores)
            
            # 排序结果
            ranking = np.argsort(scores)
            ranking = ranking[::-1]
            
            results = []
            for i in range(scores.shape[0]):
                if i < len(self.offensive_labels):
                    label = self.offensive_labels[ranking[i]]
                    score = scores[ranking[i]]
                    results.append((label, float(score)))
            
            return results
            
        except Exception as e:
            print(f"错误: 冒犯性语言检测失败: {e}")
            return None
    
    def test_sentiment_model(self, text):
        """测试情绪分析模型"""
        if self.sentiment_pipeline is None:
            print("错误: 情绪分析模型未加载")
            return None
        
        try:
            # 使用pipeline进行预测
            pipeline_result = self.sentiment_pipeline(text)
            
            # 使用完整分类方法获取详细结果
            processed_text = self.preprocess(text)
            encoded_input = self.sentiment_tokenizer(processed_text, return_tensors='pt')
            output = self.sentiment_model(**encoded_input)
            scores = output[0][0].detach().numpy()
            scores = softmax(scores)
            
            # 排序结果
            ranking = np.argsort(scores)
            ranking = ranking[::-1]
            
            detailed_results = []
            for i in range(scores.shape[0]):
                label = self.sentiment_config.id2label[ranking[i]]
                score = scores[ranking[i]]
                detailed_results.append((label, float(score)))
            
            return {
                'pipeline_result': pipeline_result,
                'detailed_results': detailed_results
            }
            
        except Exception as e:
            print(f"错误: 情绪分析失败: {e}")
            return None
    
    def run_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("开始部署和测试模型")
        print("=" * 60)
        
        # 加载模型
        offensive_loaded = self.load_offensive_model()
        sentiment_loaded = self.load_sentiment_model()
        
        if not offensive_loaded and not sentiment_loaded:
            print("错误: 所有模型加载失败")
            return False
        
        # 测试用例
        test_cases = [
            "Good night 😊",
            "Covid cases are increasing fast!",
            "You are such an idiot!",
            "I love this beautiful day",
            "This is absolutely terrible",
            "Hello @user, how are you today?",
            "Check out this link: https://example.com"
        ]
        
        print("\n" + "=" * 60)
        print("开始测试")
        print("=" * 60)
        
        for i, text in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: '{text}'")
            print("-" * 40)
            
            # 测试冒犯性语言检测
            if offensive_loaded:
                print("冒犯性语言检测结果:")
                offensive_results = self.test_offensive_model(text)
                if offensive_results:
                    for j, (label, score) in enumerate(offensive_results):
                        print(f"  {j+1}) {label}: {score:.4f}")
                else:
                    print("  检测失败")
            
            # 测试情绪分析
            if sentiment_loaded:
                print("情绪分析结果:")
                sentiment_results = self.test_sentiment_model(text)
                if sentiment_results:
                    print(f"  Pipeline结果: {sentiment_results['pipeline_result']}")
                    print("  详细结果:")
                    for j, (label, score) in enumerate(sentiment_results['detailed_results']):
                        print(f"    {j+1}) {label}: {score:.4f}")
                else:
                    print("  分析失败")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return True

def main():
    """主函数"""
    tester = ModelDeploymentTester()
    success = tester.run_tests()
    
    if success:
        print("\n✓ 模型部署和测试完成")
        return 0
    else:
        print("\n✗ 模型部署和测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
